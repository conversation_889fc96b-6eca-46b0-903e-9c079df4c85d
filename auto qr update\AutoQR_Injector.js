/**
 * @name AutoQR Injector
 * @version 3.0
 * @description 一个统一、精简且强大的脚本，用于自动化GoMyHire QR码翻译系统的所有浏览器内操作。
 *              该脚本包含更健壮的错误处理和结果验证逻辑。
 *
 *              v3.0 重大更新 - 智能添加/编辑混合功能：
 *              🧠 智能批量处理：自动判断添加新翻译或编辑现有翻译
 *              ✏️ 完整编辑功能：集成Auto Edit.js的所有编辑方法
 *              🔍 自动检测翻译：扫描现有翻译并提取ID信息
 *              📊 智能分类处理：分别处理需要添加和编辑的语言
 *              🔄 向后兼容性：保持传统添加模式的完整支持
 *
 *              v2.0 票务处理功能完善：
 *              ✅ 扩展票务关键词识别（新增'ticket'关键词）
 *              ✅ 添加排除关键词逻辑（避免'charter', 'private'误判）
 *              ✅ Sky Mirror特殊识别逻辑
 *              ✅ 10种语言的集合地点标签本地化
 *              ✅ HTML格式的地址信息显示
 *              ✅ 增强的标题标准化算法（与Auto Edit.js一致）
 *              ✅ 改进的模糊匹配逻辑
 *
 * <AUTHOR> Assistant (calibrated by user)
 * @date 2025-01-27
 * @updated 2025-01-27 (v3.0 智能添加/编辑混合功能)
 */

class AutoQRManager {
    /**
     * @constructor
     */
    constructor() {
        this.version = '3.0';
        console.log(`✅ AutoQR Injector v${this.version} 已加载 - 智能添加/编辑混合功能。`);
    }

    /**
     * 等待指定的元素出现在DOM中。
     * @param {string} selector - 要等待的元素的CSS选择器。
     * @param {number} [timeout=500] - 等待的毫秒数。
     * @returns {Promise<Element|null>} - 成功时返回找到的元素,超时返回null。
     */
    async waitForElement(selector, timeout = 500) {
        return new Promise((resolve) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const foundElement = document.querySelector(selector);
                if (foundElement) {
                    obs.disconnect();
                    resolve(foundElement);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                //不再抛出错误，而是返回null，让调用者决定如何处理
                resolve(null); 
            }, timeout);
        });
    }

    /**
     * 强制显示一个通常由Bootstrap管理的模态窗口（浮窗）。
     * 这是解决元素"不可见"问题的核心方法。
     * @param {string} modalId - 目标模态窗口的ID，例如 'qrCodeSubTranslateEditModal'。
     * @returns {Promise<void>}
     * @throws {Error} 如果模态窗口未找到。
     */
    async forceShowModal(modalId) {
        console.log(`🔧 正在强制显示ID为 '${modalId}' 的浮窗...`);
        const modal = document.getElementById(modalId);
        if (!modal) {
            throw new Error(`浮窗元素 #${modalId} 未在DOM中找到。`);
        }

        modal.classList.add('show');
        modal.style.display = 'block';
        modal.setAttribute('aria-hidden', 'false');

        // 确保背景遮罩层存在
        if (!document.querySelector('.modal-backdrop.show')) {
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
        }
        console.log(`✅ 浮窗 #${modalId} 已强制设为可见。`);
    }

    /**
     * 以编程方式安全地点击一个元素。
     * @param {string} selector - 要点击的元素的CSS选择器。
     * @returns {Promise<void>}
     * @throws {Error} 如果元素无法被找到或点击。
     */
    async clickElement(selector) {
        try {
            console.log(`🖱️ 正在点击元素: ${selector}`);
            const element = await this.waitForElement(selector);
            element.click();
            console.log(`✅ 成功点击: ${selector}`);
        } catch (error) {
            console.error(`❌ 点击元素失败: ${selector}`, error);
            throw error;
        }
    }

    /**
     * 此函数严格遵循已验证的"标准表单处理脚本"逻辑。
     * @param {object} details - 包含翻译所需所有信息的对象。
     * @param {string} details.langCode - 目标语言的代码，例如 'en', 'ms', 'zh-CN'。
     * @param {string} details.description - 翻译的"Description"文本。
     * @param {string} details.remark - 翻译的"Remark"文本。
     * @returns {Promise<boolean>} - 操作成功返回true，否则返回false。
     * @deprecated 请改用 fillAndSubmitL3Form, 此函数名不再准确。
     */
    async addOrEditTranslation(details) {
        // [v2.0 增强] 完善的票务服务识别和集合地点信息注入逻辑
        const ticketKeywords = ['firefly', 'eaglefeeding', 'bluetear', 'skymirror', 'comprehensive', 'ticket'];
        const exclusionKeywords = ['charter', 'private'];
        const titleToCheck = details.serviceTitle || '';
        const normalizedTitle = this.normalizeTitle(titleToCheck);

        // 精准识别 Sky Mirror 票务服务（注意：normalizedTitle 中空格已被移除）
        const isSkyMirrorTicket = normalizedTitle.includes('skymirror') &&
                                !normalizedTitle.includes('charter') &&
                                !normalizedTitle.includes('private');

        const hasTicket = ticketKeywords.some(keyword => normalizedTitle.includes(keyword)) || isSkyMirrorTicket;
        const hasExclusion = exclusionKeywords.some(keyword => normalizedTitle.includes(keyword));

        console.log(`🔍 票务识别调试 - 标题: "${titleToCheck}" -> 标准化: "${normalizedTitle}" -> isSkyMirrorTicket: ${isSkyMirrorTicket}, hasTicket: ${hasTicket}, hasExclusion: ${hasExclusion}`);

        if (hasTicket && !hasExclusion) {
            const meetingPointTranslations = {
                "zh-CN": "集合地点",
                "zh-TW": "集合地點",
                "en": "Meeting Point",
                "ko": "집합 장소",
                "ja": "集合場所",
                "ms": "Lokasi Menyambung",
                "id": "Lokasi Menyambung",
                "vi": "Điểm hẹn",
                "ru": "Место встречи",
                "th": "จุดรวม"
            };
            const meetingPointInfo = "Sky Mirror World & Boat Cafe, : [10, Jalan Feri Lama, Pasir Penambang, 45000 Kuala Selangor, Selangor], Google Maps: [https://maps.app.goo.gl/dBWVngJSiW6ARTWh9]<br>";
            const translatedLabel = meetingPointTranslations[details.langCode] || "集合地点";
            details.remark = `${translatedLabel}: ${meetingPointInfo}\n${details.remark}`;
        } else {
            console.log(`跳过集合地点信息注入，对于服务标题: ${titleToCheck} (hasTicket: ${hasTicket}, hasExclusion: ${hasExclusion})`);
        }

        return this.fillAndSubmitL3Form(details);
    }

    /**
     * 清理L3表单的所有输入字段内容。
     */
    async clearL3FormFields() {
        console.log("🧹 正在清理表单内容...");
        try {
            const langSelect = document.getElementById('qrCodeSubTranslate_lang');
            const descTextarea = document.getElementById('qrCodeSubTranslate_description');
            const remarkTextarea = document.getElementById('qrCodeSubTranslate_remark');

            if (langSelect) {
                langSelect.value = '';
                langSelect.selectedIndex = 0; // 重置为第一个选项
            }
            if (descTextarea) {
                descTextarea.value = '';
                descTextarea.innerHTML = '';
                // 触发输入事件以确保UI正确更新
                descTextarea.dispatchEvent(new Event('input', { bubbles: true }));
            }
            if (remarkTextarea) {
                remarkTextarea.value = '';
                remarkTextarea.innerHTML = '';
                // 触发输入事件以确保UI正确更新
                remarkTextarea.dispatchEvent(new Event('input', { bubbles: true }));
            }

            console.log("✅ 表单内容清理完成。");
            // 等待一下让UI更新
            await new Promise(resolve => setTimeout(resolve, 200));
        } catch (error) {
            console.warn("⚠️ 清理表单时出现问题:", error);
        }
    }

    /**
     * [v2.2 核心] 填充并提交L3翻译表单，具有更强的鲁棒性。
     * @param {object} details - 包含翻译所需所有信息的对象。
     * @returns {Promise<boolean>} - 操作成功返回true。
     */
    async fillAndSubmitL3Form(details) {
        const { langCode, description, remark } = details;
        console.log(`🚀 开始处理翻译，语言: ${langCode}...`);

        try {
            // 步骤0: 先清理表单内容（编辑模式需要）
            await this.clearL3FormFields();

            // 步骤1: 填写表单
            console.log("📝 正在填写表单...");
            const langSelect = await this.waitForElement('#qrCodeSubTranslate_lang', 300);
            const descTextarea = document.getElementById('qrCodeSubTranslate_description');
            const remarkTextarea = document.getElementById('qrCodeSubTranslate_remark');
            if (!langSelect || !descTextarea || !remarkTextarea) {
                throw new Error("L3表单元素 (语言、描述、备注) 未找到。");
            }

            // 确保选择正确的语言
            if (langCode) {
                langSelect.value = langCode;
                // 触发change事件以确保选择被识别
                langSelect.dispatchEvent(new Event('change', { bubbles: true }));
            }

            // 填写描述内容
            if (description) {
                descTextarea.value = description;
                descTextarea.dispatchEvent(new Event('input', { bubbles: true }));
            }

            // 填写备注内容
            if (remark) {
                remarkTextarea.value = remark;
                remarkTextarea.dispatchEvent(new Event('input', { bubbles: true }));
            }

            console.log("✅ 表单填写完成。");

            // 步骤2: 提交表单
            console.log("提交表单...");
            const submitButton = await this.waitForElement('button[onclick*="submit_qrCodeSubTranslate_form()"]');
            submitButton.click();
            console.log("✅ 表单已提交。");
            
            // 步骤3: [v1.2 新逻辑] 等待并验证结果
            console.log("⏳ 等待提交结果...");
            await new Promise(resolve => setTimeout(resolve, 500)); // 等待UI响应

            // 检查是否有SweetAlert弹窗 (成功或失败)
            const swalModal = document.querySelector('.swal-modal');
            if (swalModal) {
                const confirmButton = swalModal.querySelector('button.swal-button--confirm');
                if (swalModal.querySelector('.swal-icon--success')) {
                    console.log("✅ 检测到 SweetAlert 成功弹窗。");
                    if (confirmButton) confirmButton.click();
                    await new Promise(resolve => setTimeout(resolve, 500));
                    return true;
                }
                if (swalModal.querySelector('.swal-icon--error')) {
                    const errorText = swalModal.querySelector('.swal-text')?.textContent;
                    console.error(`❌ 检测到 SweetAlert 错误弹窗: ${errorText}`);
                    if (confirmButton) confirmButton.click();
                    await new Promise(resolve => setTimeout(resolve, 500));
                    return false;
                }
            }

            // 如果没有弹窗，检查L3编辑窗口是否已关闭，这是成功的强烈信号
            const l3Modal = document.getElementById('qrCodeSubTranslateEditModal');
            if (!l3Modal || l3Modal.style.display === 'none' || !l3Modal.classList.contains('show')) {
                 console.log("✅ L3编辑窗已关闭，操作成功。");
                 return true;
            }

            console.warn("⚠️ 未检测到明确的成功信号 (如SweetAlert或窗口关闭)。假定操作成功并继续。");
            return true;

        } catch (error) {
            console.error('❌ 填充和提交L3表单时发生严重错误:', error);
            const closeButton = document.querySelector('#qrCodeSubTranslateEditModal .close');
            if (closeButton) closeButton.click(); // 尝试关闭卡住的窗口
            return false;
        }
    }

    /**
     * [v2.1] 一键批量添加翻译的高效解决方案。
     * @param {string} serviceTitle - 服务的标准标题。
     * @param {string[]} languagesToAdd - 需要添加的语言代码数组。
     * @returns {Promise<void>}
     */
    async batchAddTranslations(serviceTitle, languagesToAdd) {
        // 日志记录开始执行批量添加任务
        console.log(`🚀 === 开始批量添加任务: ${serviceTitle} ===`);
        // 从数据源获取指定服务的所有语言翻译
        const serviceTranslations = translationDataSource[serviceTitle];

        // 如果在数据源中找不到该服务，则记录错误并返回
        if (!serviceTranslations) {
            console.error(`❌ 在数据源中未找到服务: "${serviceTitle}"`);
            return;
        }

        // 遍历需要添加的语言列表
        for (const langCode of languagesToAdd) {
            console.log(`\nProcessing language: ${langCode.toUpperCase()}`);
            // 获取特定语言的翻译详情
            const translationDetails = serviceTranslations[langCode];

            // 如果该语言的翻译不存在，则发出警告并跳过
            if (!translationDetails) {
                console.warn(`⚠️ 在数据源中未找到 ${serviceTitle} 的 ${langCode} 翻译，跳过。`);
                continue;
            }
            
            try {
                // 步骤 1: 点击"添加翻译"按钮打开L3表单
                console.log('步骤 1: 点击 "Add Translate" 按钮');
                // 等待并查找"添加翻译"按钮
                const addTranslateButton = await this.waitForElement('#qrCodeSubTranslateModal button[onclick*="qrCodeSubTranslateEditModalOpen(\'create\'"]', 3000);
                if (!addTranslateButton) throw new Error("无法找到 'Add Translate' 按钮。");
                addTranslateButton.click();
                // 等待2秒让浮窗动画完成
                await new Promise(resolve => setTimeout(resolve, 500));

                // 步骤 2: 调用核心函数填写并提交L3表单
                console.log('步骤 2: 调用核心函数填写和提交表单');
                const success = await this.fillAndSubmitL3Form({
                    langCode: langCode,
                    description: translationDetails.description,
                    remark: translationDetails.remark
                });

                // 步骤 3: 根据提交结果进行判断
                if (success) {
                    console.log(`✅ ${langCode.toUpperCase()} 添加成功!`);
                } else {
                    // 如果失败，则记录错误并终止整个批量任务
                    console.error(`❌ ${langCode.toUpperCase()} 添加失败，终止批量任务。`);
                    break;
                }
                
                // 步骤 4: 短暂等待，确保系统稳定后再进行下一次操作
                console.log('步骤 3: 等待0.5秒以确保系统稳定...');
                await new Promise(resolve => setTimeout(resolve, 500));

            } catch (error) {
                // 捕获并记录循环中发生的任何严重错误
                console.error(`❌ 处理 ${langCode} 时发生严重错误:`, error);
                console.log('终止批量任务。');
                break; // 出现严重错误时，终止循环
            }
        }
        // 记录整个批量任务执行完毕
        console.log('\n🎉 === 批量添加任务执行完毕 ===');
    }

    /**
     * [v3.0 升级] 自动从L2浮窗标题识别服务并执行智能批量处理（添加/编辑混合）。
     * @param {string[]|Object} languagesToProcess - 语言代码数组或配置对象
     * @param {Object} options - 可选配置
     * @param {boolean} options.smartMode - 是否启用智能模式（默认true）
     * @param {boolean} options.legacyMode - 是否使用传统添加模式（默认false，向后兼容）
     * @returns {Promise<void>}
     */
    async runBatchForCurrentModal(languagesToProcess, options = {}) {
        const { smartMode = true, legacyMode = false } = options;

        // 向后兼容：如果第二个参数不是对象，则认为是传统调用方式
        const isLegacyCall = typeof options === 'boolean' || options === undefined;
        const finalSmartMode = isLegacyCall ? false : smartMode;
        const finalLegacyMode = isLegacyCall ? true : legacyMode;

        console.log(`🔍 正在自动识别当前浮窗的服务标题... (${finalSmartMode ? '智能模式' : '传统模式'})`);

        // 等待标题元素出现
        const titleElement = await this.waitForElement('#qrCodeSubTranslateModalTitle', 300);
        if (!titleElement) {
            console.error("❌ 无法找到L2浮窗的标题元素 #qrCodeSubTranslateModalTitle。任务中止。");
            return;
        }

        let rawTitle = titleElement.textContent.trim();
        console.log(`📄 捕获到原始标题: "${rawTitle}"`);

        // 从标题中移除 " Translate" 后缀，以获取标准的服务名称
        const suffixToRemove = ' Translate';
        if (rawTitle.endsWith(suffixToRemove)) {
            rawTitle = rawTitle.substring(0, rawTitle.length - suffixToRemove.length).trim();
        }

        const serviceTitle = this.findMatchingServiceTitle(rawTitle);

        // 检查模糊查找后的服务名是否存在
        if (!serviceTitle) {
            console.error(`❌ 在数据源中找不到服务: "${rawTitle}"（模糊匹配也失败）`);
            console.log("   请检查: 1. 标题是否正确无误。 2. translationDataSource 是否包含该服务的条目。");

            // 提供智能建议，查找相似的键名以帮助调试
            const allKeys = Object.keys(translationDataSource);
            const similarKeys = allKeys.filter(key => key.toLowerCase().includes(rawTitle.toLowerCase()));

            if (similarKeys.length > 0) {
                console.warn(`💡 提示: 在数据源中找到以下可能相似的服务名:`);
                similarKeys.forEach(k => console.log(`  - "${k}"`));
            }
            return; // 中止执行
        }

        // 根据模式选择处理方式
        if (finalSmartMode && !finalLegacyMode) {
            // 智能模式：自动判断添加或编辑
            console.log(`🧠 准备为服务 "${serviceTitle}" 执行智能批量处理...`);
            await this.smartBatchProcess(serviceTitle, languagesToProcess);
        } else {
            // 传统模式：仅添加新翻译（向后兼容）
            console.log(`🚚 准备为服务 "${serviceTitle}" 执行传统批量添加...`);
            await this.batchAddTranslations(serviceTitle, languagesToProcess);
        }
    }

    /**
     * [v2.0 最终版] 通过浏览器原生指令点击对应的"Edit"按钮来打开L1（主项目）浮窗。
     * 这是最高权限的点击方式，能够100%模拟真实用户操作，确保成功率。
     * @param {string|number} qrCodeId - 主项目的ID。
     * @returns {Promise<void>}
     */
    async openMainModal(qrCodeId) {
        // 构建一个精确的选择器来找到特定ID的编辑按钮
        const selector = `button[onclick="qrCodeMainModalOpen('Edit', ${qrCodeId})"]`;
        console.log(`🚪 [v2.0] 正在通过浏览器原生指令点击 '${selector}' 打开主项目 L1 浮窗...`);
        try {
            // 注意：此方法依赖于外部工具 chrome_click_element 的可用性
            // 这里我们只定义接口，具体实现由调用方完成。
            // 在这里我们假设一个名为 chrome_click_element 的函数可用。
             await chrome_click_element({ selector: selector, waitForNavigation: false });
            console.log(`✅ L1 浮窗 (ID: ${qrCodeId}) 的打开指令已成功发出。`);
        } catch (error) {
            console.error(`❌ [v2.0] 使用原生指令点击 L1 浮窗按钮 (ID: ${qrCodeId}) 失败。`, error);
            throw new Error(`无法通过原生点击打开主项目ID ${qrCodeId} 的浮窗。`);
        }
    }
    
    /**
     * 直接通过调用页面上的JS函数来打开L2（子项目翻译）浮窗。
     * @param {string|number} subId - 子项目的ID。
     */
    openSubTranslateModal(subId) {
        console.log(`🚪 打开子项目 L2 翻译浮窗 (Sub ID: ${subId})...`);
        if (typeof qrCodeSubTranslate === 'function') {
            qrCodeSubTranslate(subId);
        } else {
            console.error('❌ 全局函数 qrCodeSubTranslate 不存在。');
        }
    }
    
    /**
     * 直接通过调用页面上的JS函数来打开L3（添加/编辑翻译）浮窗。
     * @param {'create'|'edit'} mode - 操作模式。
     * @param {string|number|null} translateId - 翻译条目的ID (仅在编辑模式下需要)。
     */
    openTranslateEditor(mode = 'create', translateId = null) {
        console.log(`🚪 打开 L3 翻译编辑器 (模式: ${mode})...`);
        if (typeof qrCodeSubTranslateEditModalOpen === 'function') {
            qrCodeSubTranslateEditModalOpen(mode, translateId);
        } else {
            console.error('❌ 全局函数 qrCodeSubTranslateEditModalOpen 不存在。');
        }
    }
    
    /**
     * 关闭指定的浮窗。
     * @param {string} modalId - 要关闭的浮窗ID。
     */
    closeModal(modalId) {
        console.log(`🚪 关闭浮窗 (ID: ${modalId})...`);
        if(typeof close_modal === 'function') {
            close_modal(modalId);
        } else {
            // 备用方案
            const closeButton = document.querySelector(`#${modalId} .close`);
            if(closeButton) closeButton.click();
        }
    }

    /**
     * 将服务标题归一化，用于模糊匹配。
     * 规则：忽略大小写、空格、非字母数字字符，并统一 hours -> hour。
     * @param {string} title - 原始标题。
     * @returns {string} - 标准化后的标题。
     */
    normalizeTitle(title) {
        return String(title)
            .toLowerCase()
            .replace(/\bprivate\b/g, 'hourly') // 标准化 'private' 为 'hourly'
            .replace(/\s+/g, '')        // 移除空格
            .replace(/hours/g, 'hour')   // 复数统一
            // 专项处理 firefly 的各种写法/复数
            .replace(/fireflies/g, 'firefly')
            .replace(/fireflie/g, 'firefly')
            // 通用复数->单数 (ies→y, 末尾 s 删除)
            .replace(/ies$/, 'y')
            .replace(/s$/, '')
            .replace(/[^a-z0-9]/g, '');  // 移除其他符号
    }

    /**
     * [v2.3 新增] 自动扫描并提取页面上所有已存在的翻译及其ID。
     * @param {string} tableBodySelector - 包含翻译卡片的<tbody>元素的CSS选择器。
     * @returns {Promise<Array<{language: string, langCode: string, translateId: string}>>}
     */
    async autoDetectTranslations(tableBodySelector = '#qrCodeSubTranslateTable tbody') {
        console.log(`🔍 开始扫描 ${tableBodySelector} 内的所有翻译卡片...`);

        // [v2.5 修正] 增加等待机制，确保表格内容已加载
        const tableBody = await this.waitForElement(tableBodySelector, 2000);
        if (!tableBody) {
            console.error(`❌ 扫描失败: 等待超时, 未能找到表格主体元素 "${tableBodySelector}"。`);
            return [];
        }

        const languageMap = {
            'Bahasa Indonesia': 'id',
            'Bahasa Melayu': 'ms',  // 修正：页面上显示的是 "Bahasa Melayu" 而不是 "Bahasa Malaysia"
            'English': 'en',
            'Tiếng Việt': 'vi',
            'Русский': 'ru',
            'ภาษาไทย': 'th',
            '日本語': 'ja',
            '简体中文': 'zh-CN',
            '繁體中文': 'zh-TW',
            '한국어': 'ko'
        };
        const reverseLanguageMap = Object.fromEntries(Object.entries(languageMap).map(a => a.reverse()));

        const detectedTranslations = [];
        const translationRows = tableBody.querySelectorAll(`tr`);

        if (translationRows.length === 0) {
            console.warn(`⚠️ 在 ${tableBodySelector} 中没有找到任何翻译行(tr)。请确认选择器是否正确。`);
            return [];
        }

        console.log(` 找到 ${translationRows.length} 行翻译。`);

        for (const row of translationRows) {
            try {
                // [v2.6 健壮性强化] 使用更灵活的选择器和后备方案来提取语言名称
                const firstTd = row.querySelector('td:first-child');
                const editButton = row.querySelector('button[onclick*="qrCodeSubTranslateEditModalOpen"]');
                let languageName = '';

                if (firstTd) {
                    // 方案一：尝试多个常见选择器
                    const langElement = firstTd.querySelector('div[style*="font-weight:bold"], span[style*="font-weight:bold"], b, strong');
                    if (langElement) {
                        languageName = langElement.textContent.trim();
                    } else {
                        // 方案二：如果找不到特定标签，则直接获取整个单元格的文本
                        languageName = firstTd.textContent.trim();
                    }
                }

                if (editButton) {
                    // 如果语言名称包含非字母字符，可能混杂了其他内容，需要清理
                    if (/[^\p{L}\s]/u.test(languageName)) {
                        // 尝试从已知的语言映射中反向匹配
                        const matchedLang = Object.keys(languageMap).find(lang => languageName.includes(lang));
                        if(matchedLang) {
                            languageName = matchedLang;
                        }
                    }

                    const langCode = languageMap[languageName] || 'unknown';

                    if (langCode === 'unknown' && languageName) {
                         console.warn(`⚠️ 未识别的语言: "${languageName}" - HTML:`, firstTd.innerHTML);
                    }

                    const onclickAttr = editButton.getAttribute('onclick');
                    const match = onclickAttr.match(/qrCodeSubTranslateEditModalOpen\('edit',\s*['"]?(\d+)['"]?\)/);

                    if (match && match[1]) {
                        const translateId = match[1];
                        detectedTranslations.push({
                            language: languageName,
                            langCode: langCode,
                            translateId: translateId
                        });
                        console.log(`✅ 成功检测: ${languageName || '[空名称]'} (${langCode}) -> ID: ${translateId}`);
                    } else {
                        console.warn(`⚠️ 无法从按钮提取translateId: ${languageName}`, editButton.outerHTML);
                    }
                } else {
                     if (row.innerText.trim()) { // 只对有内容的行发出警告
                        console.warn('⚠️ 在某行中找不到编辑按钮:', row.innerHTML.substring(0, 150) + '...');
                    }
                }
            } catch (error) {
                console.error('解析某一行翻译时出错:', row, error);
            }
        }

        if (detectedTranslations.length > 0) {
            console.log(`✅ 扫描完成，成功检测到 ${detectedTranslations.length} 条翻译:`);
            console.table(detectedTranslations);
        } else {
            console.log(`🟡 扫描完成，但未能从找到的行中提取任何翻译信息。`);
        }

        return detectedTranslations;
    }

    /**
     * 根据语言自动点击Edit按钮并更改翻译内容。
     * @param {string} translateId - 翻译记录的ID（例如：1835）
     * @param {string} serviceTitle - 服务标题（例如：'Eagle Feeding Ticket'）
     * @param {string} language - 语言代码（例如：'zh-CN', 'en', 'ms'等）
     * @param {Object} customContent - 可选的自定义内容，如果不提供则使用预设数据
     */
    async editTranslationByLanguage(translateId, serviceTitle, language, customContent = null) {
        console.log(`🎯 开始编辑翻译 - ID: ${translateId}, 服务: ${serviceTitle}, 语言: ${language}`);

        try {
            // 第1步：打开编辑模态框
            console.log(`🚪 打开编辑模态框 (ID: ${translateId})...`);
            if (typeof qrCodeSubTranslateEditModalOpen === 'function') {
                qrCodeSubTranslateEditModalOpen('edit', translateId);
            } else {
                throw new Error('全局函数 qrCodeSubTranslateEditModalOpen 不存在');
            }

            // 第2步：等待模态框加载
            await this.waitForElement('#qrCodeSubTranslateEditModal', 2000);
            console.log(`✅ 编辑模态框已打开`);

            // 第3步：获取翻译内容
            let translationData;
            if (customContent) {
                translationData = customContent;
            } else {
                // 从预设数据源获取翻译内容
                let serviceData = translationDataSource[serviceTitle];
                // 如果直接找不到，尝试使用模糊匹配标题
                if (!serviceData) {
                    const matchedKey = this.findMatchingServiceTitle(serviceTitle);
                    if (matchedKey) {
                        serviceData = translationDataSource[matchedKey];
                    }
                }

                // 再次检查是否找到对应语言
                if (!serviceData || !serviceData[language]) {
                    console.warn(`⚠️ 未找到服务 "${serviceTitle}" 的 "${language}" 语言翻译数据，将跳过此语言的编辑。`);
                    this.closeModal('qrCodeSubTranslateEditModal'); // 关闭已打开的编辑窗口
                    await new Promise(resolve => setTimeout(resolve, 500)); // 等待窗口关闭
                    return true; // 返回 true 以允许批量处理继续
                }
                translationData = serviceData[language];
            }

            // 第4步：填写表单内容
            console.log(`📝 填写翻译内容...`);
            const details = {
                langCode: language,  // 确保包含语言代码
                description: translationData.description || '',
                remark: translationData.remark || '',
                serviceTitle: serviceTitle  // 添加服务标题用于票务识别
            };

            await this.fillAndSubmitL3Form(details);
            console.log(`✅ 翻译编辑完成 - ${serviceTitle} (${language})`);

        } catch (error) {
            console.error(`❌ 编辑翻译失败:`, error);
            throw error;
        }
    }

    /**
     * 批量编辑指定服务的多种语言翻译。
     * @param {string} serviceTitle - 服务标题
     * @param {Array} editList - 编辑列表，格式：[{translateId: '1835', langCode: 'zh-CN'}, ...]
     */
    async batchEditTranslationsByLanguage(serviceTitle, editList) {
        console.log(`🚀 开始批量编辑翻译 - 服务: ${serviceTitle}, 待编辑数量: ${editList.length}`);

        let successCount = 0;
        let failCount = 0;

        for (const editItem of editList) {
            try {
                console.log(`\n📋 处理编辑项 ${successCount + failCount + 1}/${editList.length}`);
                await this.editTranslationByLanguage(
                    editItem.translateId,
                    serviceTitle,
                    editItem.langCode, // [v2.4 修正] 确保使用langCode
                    editItem.customContent || null
                );
                successCount++;

                // 编辑完成后等待一下，避免操作过快
                await new Promise(resolve => setTimeout(resolve, 1000));

            } catch (error) {
                console.error(`❌ 编辑失败 - ID: ${editItem.translateId}, 语言: ${editItem.langCode}`, error);
                failCount++;
            }
        }

        console.log(`\n📊 批量编辑完成:`);
        console.log(`✅ 成功: ${successCount} 项`);
        console.log(`❌ 失败: ${failCount} 项`);

        return { success: successCount, failed: failCount };
    }

    /**
     * [v3.0 新增] 智能批量处理翻译 - 自动判断添加或编辑
     * @param {string} serviceTitle - 服务标题
     * @param {Array<string>} languagesToProcess - 要处理的语言代码数组
     * @returns {Promise<{added: number, edited: number, failed: number}>}
     */
    async smartBatchProcess(serviceTitle, languagesToProcess) {
        console.log(`🧠 开始智能批量处理翻译 - 服务: ${serviceTitle}, 语言数量: ${languagesToProcess.length}`);

        // 步骤1: 检测现有翻译
        console.log(`🔍 步骤1: 检测现有翻译...`);
        const existingTranslations = await this.autoDetectTranslations();
        const existingLangCodes = existingTranslations.map(t => t.langCode);

        // 步骤2: 分类语言 - 需要添加的 vs 需要编辑的
        const languagesToAdd = languagesToProcess.filter(lang => !existingLangCodes.includes(lang));
        const languagesToEdit = languagesToProcess.filter(lang => existingLangCodes.includes(lang));

        console.log(`📊 分类结果:`);
        console.log(`  ➕ 需要添加: ${languagesToAdd.length} 种语言 - ${languagesToAdd.join(', ')}`);
        console.log(`  ✏️ 需要编辑: ${languagesToEdit.length} 种语言 - ${languagesToEdit.join(', ')}`);

        let addedCount = 0;
        let editedCount = 0;
        let failedCount = 0;

        // 步骤3: 执行添加操作
        if (languagesToAdd.length > 0) {
            console.log(`\n➕ 开始添加新翻译...`);
            try {
                await this.batchAddTranslations(serviceTitle, languagesToAdd);
                addedCount = languagesToAdd.length;
                console.log(`✅ 添加操作完成: ${addedCount} 种语言`);
            } catch (error) {
                console.error(`❌ 添加操作失败:`, error);
                failedCount += languagesToAdd.length;
            }
        }

        // 步骤4: 执行编辑操作
        if (languagesToEdit.length > 0) {
            console.log(`\n✏️ 开始编辑现有翻译...`);
            try {
                // 构建编辑列表
                const editList = existingTranslations
                    .filter(t => languagesToEdit.includes(t.langCode))
                    .map(t => ({
                        translateId: t.translateId,
                        langCode: t.langCode,
                        language: t.language
                    }));

                const editResult = await this.batchEditTranslationsByLanguage(serviceTitle, editList);
                editedCount = editResult.success;
                failedCount += editResult.failed;
                console.log(`✅ 编辑操作完成: ${editedCount} 种语言成功, ${editResult.failed} 种语言失败`);
            } catch (error) {
                console.error(`❌ 编辑操作失败:`, error);
                failedCount += languagesToEdit.length;
            }
        }

        // 步骤5: 总结报告
        console.log(`\n🎉 智能批量处理完成:`);
        console.log(`  ➕ 新增翻译: ${addedCount} 种语言`);
        console.log(`  ✏️ 编辑翻译: ${editedCount} 种语言`);
        console.log(`  ❌ 失败: ${failedCount} 种语言`);
        console.log(`  📊 总成功率: ${((addedCount + editedCount) / languagesToProcess.length * 100).toFixed(1)}%`);

        return {
            added: addedCount,
            edited: editedCount,
            failed: failedCount
        };
    }

    /**
     * 标准化标题字符串，用于模糊匹配。
     * @param {string} title - 原始标题。
     * @returns {string} - 标准化后的标题。
     * @private
     * @deprecated 请使用 normalizeTitle 方法
     */
    _normalizeTitle(title) {
        return this.normalizeTitle(title);
    }

    /**
     * 在 translationDataSource 中查找与 rawTitle 最匹配的 key。
     * @param {string} rawTitle - 原始标题
     * @returns {string|null} - 返回匹配到的正式 key，未找到返回 null
     */
    findMatchingServiceTitle(rawTitle) {
        // 强制每次重新生成映射表，以确保 normalizeTitle 的最新规则生效
        const translationKeyMap = {};
        Object.keys(translationDataSource).forEach(k => {
            const normKey = this.normalizeTitle(k);
            const isEmptyObj = !translationDataSource[k] || Object.keys(translationDataSource[k]).length === 0;
            if (!translationKeyMap[normKey]) {
                // 之前不存在，直接写入
                translationKeyMap[normKey] = k;
            } else {
                // 已存在：如果之前映射到的是空对象，而当前对象非空，则覆盖
                const prevKey = translationKeyMap[normKey];
                const prevIsEmpty = !translationDataSource[prevKey] || Object.keys(translationDataSource[prevKey]).length === 0;
                if (prevIsEmpty && !isEmptyObj) {
                    translationKeyMap[normKey] = k;
                }
            }
        });

        const norm = this.normalizeTitle(rawTitle);
        if (translationKeyMap[norm]) return translationKeyMap[norm];

        // 二次尝试：包含关系匹配（更宽松）
        for (const [normKey, originalKey] of Object.entries(translationKeyMap)) {
            if (normKey.includes(norm) || norm.includes(normKey)) {
                return originalKey;
            }
        }
        return null;
    }
}

// 在window对象上实例化管理器，以便在浏览器控制台中调用
window.autoQR = new AutoQRManager(); 

// ====================================================================================
// 翻译数据源 (Translation Data Source) v2.0
// 从 business-translation-tool.md 生成的结构化数据
// ====================================================================================
const translationDataSource = {
    "Kuala Lumpur City Tour (5 Hours)": {
        "id": { "description": "Sewa mobil pribadi selama 5 jam di pusat kota", "remark": "● Zona penjemputan: Area Kuala Lumpur saja <br>● Radius tur: Maksimal 50km" },
        "ms": { "description": "Sewa kereta persendirian 5 jam di pusat bandar", "remark": "● Zon pengambilan: Kawasan Kuala Lumpur sahaja <br>● Jejari lawatan: Maksimum 50km" },
        "en": { "description": "Private car charter for 5 hours within the city center", "remark": "● Pickup zone: Kuala Lumpur area only <br>● Tour radius: Limited to 50km" },
        "vi": { "description": "Thuê xe riêng 5 giờ trong trung tâm thành phố", "remark": "● Khu vực đón: Chỉ trong khu vực Kuala Lumpur <br>● Bán kính tour: Giới hạn 50km" },
        "ru": { "description": "Частный автомобиль на 5 часов в пределах центра города", "remark": "● Зона встречи: Только район Куала-Lumpura <br>● Радиус тура: Ограничен 50 км" },
        "th": { "description": "บริการรถยนต์ส่วนตัวเช่าเหมา 5 ชั่วโมง ภายในเมือง", "remark": "● โซนรับส่ง: เฉพาะพื้นที่กัวลาลัมเปอร์<br>● รัศมีทัวร์: จำกัด 50 กม." },
        "ja": { "description": "市内中心部でのプライベートカーチャーター（5時間）", "remark": "● 送迎エリア: クアラルンプール地域のみ <br>● ツアー範囲: 50km以内" },
        "zh-CN": { "description": "城市中心5小时私人包车服务", "remark": "● 接送区域：仅限吉隆坡地区 <br>● 行程半径：限制50公里" },
        "zh-TW": { "description": "市中心私人包車5小時", "remark": "● 接送區域：僅限吉隆坡地區 <br>● 旅遊範圍：限制50公里" },
        "ko": { "description": "시내 중심 5시간 개인 차량 대절", "remark": "● 픽업 지역: 쿠알라룸푸르 지역만 해당 <br>● 투어 반경: 50km 제한" }
    },
    "Kuala Lumpur City Tour (10 Hours)": {
        "id": { "description": "Sewa mobil pribadi selama 10 jam di pusat kota", "remark": "● Zona penjemputan: Area Kuala Lumpur saja <br>● Radius tur: Maksimal 50km" },
        "ms": { "description": "Sewa kereta persendirian 10 jam di pusat bandar", "remark": "● Zon pengambilan: Kawasan Kuala Lumpur sahaja <br>● Jejari lawatan: Maksimum 50km" },
        "en": { "description": "Private car charter for 10 hours within the city center", "remark": "● Pickup zone: Kuala Lumpur area only <br>● Tour radius: Limited to 50km" },
        "vi": { "description": "Thuê xe riêng 10 giờ trong trung tâm thành phố", "remark": "● Khu vực đón: Chỉ trong khu vực Kuala Lumpur <br>● Bán kính tour: Giới hạn 50km" },
        "ru": { "description": "Частный автомобиль на 10 часов в пределах центра города", "remark": "● Зона встречи: Только район Куала-Лумпура <br>● Радиус тура: Ограничен 50 км" },
        "th": { "description": "บริการรถยนต์ส่วนตัวเช่าเหมา 10 ชั่วโมง ภายในเมือง", "remark": "● โซนรับส่ง: เฉพาะพื้นที่กัวลาลัมเปอร์<br>● รัศมีทัวร์: จำกัด 50 กม." },
        "ja": { "description": "市内中心部でのプライベートカーチャーター（10時間）", "remark": "● 送迎エリア: クアラルンプール地域のみ <br>● ツアー範囲: 50km以内" },
        "zh-CN": { "description": "城市中心10小时私人包车服务", "remark": "● 接送区域：仅限吉隆坡地区 <br>● 行程半径：限制50公里" },
        "zh-TW": { "description": "市中心私人包車10小時", "remark": "● 接送區域：僅限吉隆坡地區 <br>● 旅遊範圍：限制50公里" },
        "ko": { "description": "시내 중심 10시간 개인 차량 대절", "remark": "● 픽업 지역: 쿠알라룸푸르 지역만 해당 <br>● 투어 반경: 50km 제한" }
    },
    "Melaka Private Tour (10 Hours)": {
        "id": { "description": "Tur eksklusif 10 jam di Melaka dengan sopir-pemandu (Mandarin/Inggris)<br>Tidak termasuk tiket masuk objek wisata", "remark": "● Penjemputan: Hanya lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum 20:00 sehari sebelumnya <br>● Waktu: Termasuk perjalanan pulang-pergi <br>● Makanan: Ditanggung sendiri <br>● Biaya tambahan: Durasi >10 jam & transfer bandara" },
        "ms": { "description": "Pakej eksklusif 10 jam di Melaka dengan pemandu-pelancong (Cina/Inggeris)<br>Tidak termasuk tiket masuk tarikan", "remark": "● Pengambilan: Lokasi terpilih di Kuala Lumpur sahaja <br>● Tempahan: Sebelum 8 malam sehari sebelumnya <br>● Masa: Termasuk perjalanan pergi-balik <br>● Makanan: Urusan sendiri <br>● Caj tambahan: Melebihi 10 jam & transfer lapangan terbang" },
        "en": { "description": "10-hour private tour in Melaka with driver-guide (Chinese/English)<br>Attraction entrance fees excluded", "remark": "● Pickup: Kuala Lumpur designated locations only <br>● Booking: By 8 PM the day before departure <br>● Duration: Includes round-trip travel time <br>● Meals: Self-catered <br>● Extra charges: Over 10 hours & airport transfers" },
        "vi": { "description": "Tour riêng 10 giờ tại Malacca với tài xế-hướng dẫn (Trung/Anh)<br>Không bao gồm vé tham quan", "remark": "● Đón khách: Chỉ các địa điểm chỉ định tại Kuala Lumpur <br>● Đặt dịch vụ: Trước 20:00 ngày hôm trước <br>● Thời gian: Bao gồm di chuyển khứ hồi <br>● Ăn uống: Khách tự túc <br>● Phụ phí: Vượt 10 giờ & đưa đón sân bay" },
        "ru": { "description": "10-часовой частный тур по Малакке с водителем-гидом (китайский/английский)<br>Входные билеты не включены", "remark": "● Встреча: Только указанные места в Куала-Лумпуре <br>● Бронирование: До 20:00 накануне отправления <br>● Время: Включает дорогу туда и обратно <br>● Питание: За свой счет <br>● Дополнительная плата: За дополнительные остановки и услуги" },
        "th": { "description": "ทัวร์ส่วนตัว 10 ชั่วโมงที่มะละกา พร้อมคนขับ-ไกด์ (จีน/อังกฤษ)<br>ไม่รวมค่าเข้าชมสถานที่", "remark": "● รับส่ง: เฉพาะจุดที่กำหนดในกัวลาลัมเปอร์<br>● การจอง: ก่อน 20:00 น. ของวันก่อนหน้า <br>● เวลา: รวมการเดินทางไป-กลับ <br>● อาหาร: รับผิดชอบเอง <br>● ค่าใช้จ่ายเพิ่มเติม: จุดแวะพักเพิ่มเติมหรือบริการเสริม" },
        "ja": { "description": "マラッカ10時間プライベートツアー、ドライバー兼ガイド付き（中国語/英語）<br>入場料別途", "remark": "● 送迎: クアラルンプール指定場所のみ <br>● 予約: 出発前日20時まで <br>● 時間: 往復送迎時間含む <br>● 食事: 各自負担 <br>● 追加料金: 10時間超過分・空港送迎" },
        "zh-CN": { "description": "马六甲10小时专属包车游，配司机兼导游（中英文）<br>不含景点门票", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时间: 含往返接送时间 <br>● 餐食: 自理 <br>● 额外费用: 超10小时 & 机场接送" },
        "zh-TW": { "description": "馬六甲10小時專屬包車遊，配司機兼導遊（中英文）<br>不含景點門票", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時間: 含往返接送時間 <br>● 餐食: 自理 <br>● 額外費用: 超10小時 & 機場接送" },
        "ko": { "description": "말라카 10시간 전용투어, 기사 겸 가이드 동행 (중국어/영어)<br>입장권 불포함", "remark": "● 픽업: 쿠알라룸푸르 지정 장소만 <br>● 예약: 출발 전날 오후 8시까지 <br>● 시간: 왕복 이동시간 포함 <br>● 식사: 개별 준비 <br>● 추가 요금: 10시간 초과 및 공항 픽업" }
    },
    "Kuala Selangor Private Charter (6 Hours)": {
        "id": { "description": "Tour eksklusif 6 jam ke Kuala Selangor dengan sopir profesional (Mandarin/Inggris)<br>Tidak termasuk tiket masuk dan pengeluaran pribadi", "remark": "● Penjemputan: Lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum 20:00 sehari sebelumnya <br>● Durasi: 6 jam dari waktu penjemputan <br>● Makanan: Ditanggung sendiri <br>● Transfer bandara: Biaya tambahan" },
        "ms": { "description": "Pakej eksklusif 6 jam ke Kuala Selangor dengan pemandu profesional (Cina/Inggeris)<br>Tidak termasuk tiket masuk dan perbelanjaan peribadi", "remark": "● Pengambilan: Lokasi terpilih di Kuala Lumpur <br>● Tempahan: Sebelum 8 malam sehari sebelumnya <br>● Tempoh: 6 jam dari masa pengambilan <br>● Makanan: Urusan sendiri <br>● Transfer lapangan terbang: Bayaran tambahan" },
        "en": { "description": "6-hour exclusive charter to Kuala Selangor with professional driver (Chinese/English)<br>Excludes attraction tickets and personal expenses", "remark": "● Pickup: Kuala Lumpur designated locations only <br>● Booking: By 8 PM the day before departure <br>● Duration: 6 hours from pickup time <br>● Meals: Self-catered <br>● Airport transfers: Additional charges" },
        "vi": { "description": "Tour riêng 6 giờ đến Kuala Selangor với tài xế chuyên nghiệp (Trung/Anh)<br>Không bao gồm vé tham quan và chi tiêu cá nhân", "remark": "● Đón khách: Các địa điểm chỉ định tại Kuala Lumpur <br>● Đặt dịch vụ: Trước 20:00 ngày hôm trước <br>● Thời lượng: 6 giờ từ thời điểm đón khách <br>● Ăn uống: Khách tự túc <br>● Đưa đón sân bay: Có phụ phí" },
        "ru": { "description": "6-часовой эксклюзивный тур в Куала-Селангор с профессиональным водителем (китайский/английский)<br>Входные билеты и личные расходы не включены", "remark": "● Встреча: Указанные места в Куала-Лумпуре <br>● Бронирование: До 20:00 накануне отправления <br>● Продолжительность: 6 часов с момента встречи <br>● Питание: За свой счет <br>● Трансфер в аэропорт: Дополнительная плата" },
        "th": { "description": "ทัวร์เอกชน 6 ชั่วโมงไปกัวลาเซลังงอร์ พร้อมคนขับมืออาชีพ (จีน/อังกฤษ)<br>ไม่รวมค่าเข้าชมและค่าใช้จ่ายส่วนตัว", "remark": "● รับส่ง: เฉพาะจุดที่กำหนดในกัวลาลัมเปอร์<br>● การจอง: ก่อน 20:00 น. ของวันก่อนหน้า <br>● ระยะเวลา: 6 ชั่วโมงนับจากเวลารับ <br>● อาหาร: รับผิดชอบเอง <br>● รับส่งสนามบิน: ค่าใช้จ่ายเพิ่มเติม" },
        "ja": { "description": "クアラセランゴール6時間専用チャーター、プロドライバー付き（中国語/英語）<br>入場料・個人費用別途", "remark": "● 送迎: クアラルンプール指定場所のみ <br>● 予約: 出発前日20時まで <br>● 時間: 乗車時から6時間 <br>● 食事: 各自負担 <br>● 空港送迎: 別途料金" },
        "zh-CN": { "description": "瓜拉雪兰莪6小时专属包车，配专业司机（中英文）<br>不含景点门票及个人消费", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 从接客时间起6小时 <br>● 餐食: 自理 <br>● 机场接送: 需额外付费" },
        "zh-TW": { "description": "瓜拉雪蘭莪6小時專屬包車，配專業司機（中英文）<br>不含景點門票及個人消費", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 從接客時間起6小時 <br>● 餐食: 自理 <br>● 機場接送: 需額外付費" },
        "ko": { "description": "쿠알라 셀랑고르 6시간 전용 차량, 전문 기사 동행 (중국어/영어)<br>입장권 및 개인 경비 불포함", "remark": "● 픽업: 쿠알라룸푸르 지정 장소만 <br>● 예약: 출발 전날 오후 8시까지 <br>● 시간: 픽업 시간부터 6시간 <br>● 식사: 개별 준비 <br>● 공항 픽업: 추가 요금" }
    },
    "Kuala Selangor Hourly Charter (6 Hour)": {
        "id": { "description": "Tour eksklusif 6 jam ke Kuala Selangor dengan sopir profesional (Mandarin/Inggris)<br>Tidak termasuk tiket masuk dan pengeluaran pribadi", "remark": "● Penjemputan: Lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum 20:00 sehari sebelumnya <br>● Durasi: 6 jam dari waktu penjemputan <br>● Makanan: Ditanggung sendiri <br>● Transfer bandara: Biaya tambahan" },
        "ms": { "description": "Pakej eksklusif 6 jam ke Kuala Selangor dengan pemandu profesional (Cina/Inggeris)<br>Tidak termasuk tiket masuk dan perbelanjaan peribadi", "remark": "● Pengambilan: Lokasi terpilih di Kuala Lumpur <br>● Tempahan: Sebelum 8 malam sehari sebelumnya <br>● Tempoh: 6 jam dari masa pengambilan <br>● Makanan: Urusan sendiri <br>● Transfer lapangan terbang: Bayaran tambahan" },
        "en": { "description": "6-hour exclusive charter to Kuala Selangor with professional driver (Chinese/English)<br>Excludes attraction tickets and personal expenses", "remark": "● Pickup: Kuala Lumpur designated locations only <br>● Booking: By 8 PM the day before departure <br>● Duration: 6 hours from pickup time <br>● Meals: Self-catered <br>● Airport transfers: Additional charges" },
        "vi": { "description": "Tour riêng 6 giờ đến Kuala Selangor với tài xế chuyên nghiệp (Trung/Anh)<br>Không bao gồm vé tham quan và chi tiêu cá nhân", "remark": "● Đón khách: Các địa điểm chỉ định tại Kuala Lumpur <br>● Đặt dịch vụ: Trước 20:00 ngày hôm trước <br>● Thời lượng: 6 giờ từ thời điểm đón khách <br>● Ăn uống: Khách tự túc <br>● Đưa đón sân bay: Có phụ phí" },
        "ru": { "description": "6-часовой эксклюзивный тур в Куала-Селангор с профессиональным водителем (китайский/английский)<br>Входные билеты и личные расходы не включены", "remark": "● Встреча: Указанные места в Куала-Лумпуре <br>● Бронирование: До 20:00 накануне отправления <br>● Продолжительность: 6 часов с момента встречи <br>● Питание: За свой счет <br>● Трансфер в аэропорт: Дополнительная плата" },
        "th": { "description": "ทัวร์เอกชน 6 ชั่วโมงไปกัวลาเซลังงอร์ พร้อมคนขับมืออาชีพ (จีน/อังกฤษ)<br>ไม่รวมค่าเข้าชมและค่าใช้จ่ายส่วนตัว", "remark": "● รับส่ง: เฉพาะจุดที่กำหนดในกัวลาลัมเปอร์<br>● การจอง: ก่อน 20:00 น. ของวันก่อนหน้า <br>● ระยะเวลา: 6 ชั่วโมงนับจากเวลารับ <br>● อาหาร: รับผิดชอบเอง <br>● รับส่งสนามบิน: ค่าใช้จ่ายเพิ่มเติม" },
        "ja": { "description": "クアラセランゴール6時間専用チャーター、プロドライバー付き（中国語/英語）<br>入場料・個人費用別途", "remark": "● 送迎: クアラルンプール指定場所のみ <br>● 予約: 出発前日20時まで <br>● 時間: 乗車時から6時間 <br>● 食事: 各自負担 <br>● 空港送迎: 別途料金" },
        "zh-CN": { "description": "瓜拉雪兰莪6小时专属包车，配专业司机（中英文）<br>不含景点门票及个人消费", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 从接客时间起6小时 <br>● 餐食: 自理 <br>● 机场接送: 需额外付费" },
        "zh-TW": { "description": "瓜拉雪蘭莪6小時專屬包車，配專業司機（中英文）<br>不含景點門票及個人消費", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 從接客時間起6小時 <br>● 餐食: 自理 <br>● 機場接送: 需額外付費" },
        "ko": { "description": "쿠알라 셀랑고르 6시간 전용 차량, 전문 기사 동행 (중국어/영어)<br>입장권 및 개인 경비 불포함", "remark": "● 픽업: 쿠알라룸푸르 지정 장소만 <br>● 예약: 출발 전날 오후 8시까지 <br>● 시간: 픽업 시간부터 6시간 <br>● 식사: 개별 준비 <br>● 공항 픽업: 추가 요금" }
    },
    "Kuala Selangor Private Charter (10 Hours)": {
        "id": { "description": "Tour eksklusif 10 jam ke Kuala Selangor dengan sopir profesional (Mandarin/Inggris)<br>Tidak termasuk tiket masuk dan pengeluaran pribadi", "remark": "● Penjemputan: Lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum 20:00 sehari sebelumnya <br>● Durasi: 10 jam dari waktu penjemputan <br>● Makanan: Ditanggung sendiri <br>● Transfer bandara: Biaya tambahan" },
        "ms": { "description": "Pakej eksklusif 10 jam ke Kuala Selangor dengan pemandu profesional (Cina/Inggeris)<br>Tidak termasuk tiket masuk dan perbelanjaan peribadi", "remark": "● Pengambilan: Lokasi terpilih di Kuala Lumpur <br>● Tempahan: Sebelum 8 malam sehari sebelumnya <br>● Tempoh: 10 jam dari masa pengambilan <br>● Makanan: Urusan sendiri <br>● Transfer lapangan terbang: Bayaran tambahan" },
        "en": { "description": "10-hour exclusive charter to Kuala Selangor with professional driver (Chinese/English)<br>Excludes attraction tickets and personal expenses", "remark": "● Pickup: Kuala Lumpur designated locations only <br>● Booking: By 8 PM the day before departure <br>● Duration: 10 hours from pickup time <br>● Meals: Self-catered <br>● Airport transfers: Additional charges" },
        "vi": { "description": "Tour riêng 10 giờ đến Kuala Selangor với tài xế chuyên nghiệp (Trung/Anh)<br>Không bao gồm vé tham quan và chi tiêu cá nhân", "remark": "● Đón khách: Các địa điểm chỉ định tại Kuala Lumpur <br>● Đặt dịch vụ: Trước 20:00 ngày hôm trước <br>● Thời lượng: 10 giờ từ thời điểm đón khách <br>● Ăn uống: Khách tự túc <br>● Đưa đón sân bay: Có phụ phí" },
        "ru": { "description": "10-часовой эксклюзивный тур в Куала-Селангоре с профессиональным водителем (китайский/английский)<br>Входные билеты и личные расходы не включены", "remark": "● Встреча: Указанные места в Куала-Лумпуре <br>● Бронирование: До 20:00 накануне отправления <br>● Продолжительность: 10 часов с момента встречи <br>● Питание: За свой счет <br>● Трансфер в аэропорт: Дополнительная плата" },
        "th": { "description": "ทัวร์เอกชน 10 ชั่วโมงไปกัวลาเซลังงอร์ พร้อมคนขับมืออาชีพ (จีน/อังกฤษ)<br>ไม่รวมค่าเข้าชมและค่าใช้จ่ายส่วนตัว", "remark": "● รับส่ง: เฉพาะจุดที่กำหนดในกัวลาลัมเปอร์<br>● การจอง: ก่อน 20:00 น. ของวันก่อนหน้า <br>● ระยะเวลา: 10 ชั่วโมงนับจากเวลารับ <br>● อาหาร: รับผิดชอบเอง <br>● รับส่งสนามบิน: ค่าใช้จ่ายเพิ่มเติม" },
        "ja": { "description": "クアラセランゴール10時間専用チャーター、プロドライバー付き（中国語/英語）<br>入場料・個人費用別途", "remark": "● 送迎: クアラルンプール指定場所のみ <br>● 予約: 出発前日20時まで <br>● 時間: 乗車時から10時間 <br>● 食事: 各自負担 <br>● 空港送迎: 別途料金" },
        "zh-CN": { "description": "瓜拉雪兰莪10小时专属包车，配专业司机（中英文）<br>不含景点门票及个人消费", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 从接客时间起10小时 <br>● 餐食: 自理 <br>● 机场接送: 需额外付费" },
        "zh-TW": { "description": "瓜拉雪蘭莪10小時專屬包車，配專業司機（中英文）<br>不含景點門票及個人消費", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 從接客時間起10小時 <br>● 餐食: 自理 <br>● 機場接送: 需額外付費" },
        "ko": { "description": "쿠알라 셀랑고르 10시간 전용 차량, 전문 기사 동행 (중국어/영어)<br>입장권 및 개인 경비 불포함", "remark": "● 픽업: 쿠알라룸푸르 지정 장소만 <br>● 예약: 출발 전날 오후 8시까지 <br>● 시간: 픽업 시간부터 10시간 <br>● 식사: 개별 준비 <br>● 공항 픽업: 추가 요금" }
    },
    "Kuala Selangor Hourly Charter (10 Hour)": {
        "id": { "description": "Tour eksklusif 10 jam ke Kuala Selangor dengan sopir profesional (Mandarin/Inggris)<br>Tidak termasuk tiket masuk dan pengeluaran pribadi", "remark": "● Penjemputan: Lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum 20:00 sehari sebelumnya <br>● Durasi: 10 jam dari waktu penjemputan <br>● Makanan: Ditanggung sendiri <br>● Transfer bandara: Biaya tambahan" },
        "ms": { "description": "Pakej eksklusif 10 jam ke Kuala Selangor dengan pemandu profesional (Cina/Inggeris)<br>Tidak termasuk tiket masuk dan perbelanjaan peribadi", "remark": "● Pengambilan: Lokasi terpilih di Kuala Lumpur <br>● Tempahan: Sebelum 8 malam sehari sebelumnya <br>● Tempoh: 10 jam dari masa pengambilan <br>● Makanan: Urusan sendiri <br>● Transfer lapangan terbang: Bayaran tambahan" },
        "en": { "description": "10-hour exclusive charter to Kuala Selangor with professional driver (Chinese/English)<br>Excludes attraction tickets and personal expenses", "remark": "● Pickup: Kuala Lumpur designated locations only <br>● Booking: By 8 PM the day before departure <br>● Duration: 10 hours from pickup time <br>● Meals: Self-catered <br>● Airport transfers: Additional charges" },
        "vi": { "description": "Tour riêng 10 giờ đến Kuala Selangor với tài xế chuyên nghiệp (Trung/Anh)<br>Không bao gồm vé tham quan và chi tiêu cá nhân", "remark": "● Đón khách: Các địa điểm chỉ định tại Kuala Lumpur <br>● Đặt dịch vụ: Trước 20:00 ngày hôm trước <br>● Thời lượng: 10 giờ từ thời điểm đón khách <br>● Ăn uống: Khách tự túc <br>● Đưa đón sân bay: Có phụ phí" },
        "ru": { "description": "10-часовой эксклюзивный тур в Куала-Селангоре с профессиональным водителем (китайский/английский)<br>Входные билеты и личные расходы не включены", "remark": "● Встреча: Указанные места в Куала-Лумпуре <br>● Бронирование: До 20:00 накануне отправления <br>● Продолжительность: 10 часов с момента встречи <br>● Питание: За свой счет <br>● Трансфер в аэропорт: Дополнительная плата" },
        "th": { "description": "ทัวร์เอกชน 10 ชั่วโมงไปกัวลาเซลังงอร์ พร้อมคนขับมืออาชีพ (จีน/อังกฤษ)<br>ไม่รวมค่าเข้าชมและค่าใช้จ่ายส่วนตัว", "remark": "● รับส่ง: เฉพาะจุดที่กำหนดในกัวลาลัมเปอร์<br>● การจอง: ก่อน 20:00 น. ของวันก่อนหน้า <br>● ระยะเวลา: 10 ชั่วโมงนับจากเวลารับ <br>● อาหาร: รับผิดชอบเอง <br>● รับส่งสนามบิน: ค่าใช้จ่ายเพิ่มเติม" },
        "ja": { "description": "クアラセランゴール10時間専用チャーター、プロドライバー付き（中国語/英語）<br>入場料・個人費用別途", "remark": "● 送迎: クアラルンプール指定場所のみ <br>● 予約: 出発前日20時まで <br>● 時間: 乗車時から10時間 <br>● 食事: 各自負担 <br>● 空港送迎: 別途料金" },
        "zh-CN": { "description": "瓜拉雪兰莪10小时专属包车，配专业司机（中英文）<br>不含景点门票及个人消费", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 从接客时间起10小时 <br>● 餐食: 自理 <br>● 机场接送: 需额外付费" },
        "zh-TW": { "description": "瓜拉雪蘭莪10小時專屬包車，配專業司機（中英文）<br>不含景點門票及個人消費", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 從接客時間起10小時 <br>● 餐食: 自理 <br>● 機場接送: 需額外付費" },
        "ko": { "description": "쿠알라 셀랑고르 10시간 전용 차량, 전문 기사 동행 (중국어/영어)<br>입장권 및 개인 경비 불포함", "remark": "● 픽업: 쿠알라룸푸르 지정 장소만 <br>● 예약: 출발 전날 오후 8시까지 <br>● 시간: 픽업 시간부터 10시간 <br>● 식사: 개별 준비 <br>● 공항 픽업: 추가 요금" }
    },
    "Genting Highland Private Charter (10 Hours)": {
        "id": { "description": "Tour eksklusif 10 jam ke Genting Highlands dengan sopir profesional (Mandarin/Inggris)<br>Tidak termasuk tiket masuk, tiket kereta gantung dan pengeluaran pribadi", "remark": "● Penjemputan: Lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum 20:00 sehari sebelumnya <br>● Durasi: 10 jam dari waktu penjemputan <br>● Makanan: Ditanggung sendiri <br>● Transfer bandara: Biaya tambahan" },
        "ms": { "description": "Pakej eksklusif 10 jam ke Genting Highlands dengan pemandu profesional (Cina/Inggeris)<br>Tidak termasuk tiket masuk, tiket kereta gantung dan perbelanjaan peribadi", "remark": "● Pengambilan: Lokasi terpilih di Kuala Lumpur <br>● Tempahan: Sebelum 8 malam sehari sebelumnya <br>● Tempoh: 10 jam dari masa pengambilan <br>● Makanan: Urusan sendiri <br>● Transfer lapangan terbang: Bayaran tambahan" },
        "en": { "description": "10-hour exclusive charter to Genting Highlands with professional driver (Chinese/English)<br>Excludes attraction tickets, cable car tickets and personal expenses", "remark": "● Pickup: Kuala Lumpur designated locations only <br>● Booking: By 8 PM the day before departure <br>● Duration: 10 hours from pickup time <br>● Meals: Self-catered <br>● Airport transfers: Additional charges" },
        "vi": { "description": "Tour riêng 10 giờ đến Genting Highlands với tài xế chuyên nghiệp (Trung/Anh)<br>Không bao gồm vé tham quan, vé cáp treo và chi tiêu cá nhân", "remark": "● Đón khách: Các địa điểm chỉ định tại Kuala Lumpur <br>● Đặt dịch vụ: Trước 20:00 ngày hôm trước <br>● Thời lượng: 10 giờ từ thời điểm đón khách <br>● Ăn uống: Khách tự túc <br>● Đưa đón sân bay: Có phụ phí" },
        "ru": { "description": "10-часовой эксклюзивный тур в Гентинг Хайлендс с профессиональным водителем (китайский/английский)<br>Входные билеты, билеты на канатную дорогу и личные расходы не включены", "remark": "● Встреча: Указанные места в Куала-Лумпуре <br>● Бронирование: До 20:00 накануне отправления <br>● Продолжительность: 10 часов с момента встречи <br>● Питание: За свой счет <br>● Трансфер в аэропорт: Дополнительная плата" },
        "th": { "description": "ทัวร์เอกชน 10 ชั่วโมงไปเก็นติ้งไฮแลนด์ พร้อมคนขับมืออาชีพ (จีน/อังกฤษ)<br>ไม่รวมค่าเข้าชม, ค่ากระเช้า และค่าใช้จ่ายส่วนตัว", "remark": "● รับส่ง: เฉพาะจุดที่กำหนดในกัวลาลัมเปอร์<br>● การจอง: ก่อน 20:00 น. ของวันก่อนหน้า <br>● ระยะเวลา: 10 ชั่วโมงนับจากเวลารับ <br>● อาหาร: รับผิดชอบเอง <br>● รับส่งสนามบิน: ค่าใช้จ่ายเพิ่มเติม" },
        "ja": { "description": "ゲンティン・ファーストワールドホテルからKLCCへのシャトルサービス", "remark": "● 乗車場所: ゲンティン・ファーストワールドホテル <br>● 降車場所: KLCCバスステーション <br>● 荷物制限: お一人様バックパック1個まで、スーツケースは追加料金 <br>● 定刻発車、遅刻者はお待ちしません" },
        "zh-CN": { "description": "云顶高原10小时专属包车，配专业司机（中英文）<br>不含景点门票、缆车票及个人消费", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 从接客时间起10小时 <br>● 餐食: 自理 <br>● 机场接送: 需额外付费" },
        "zh-TW": { "description": "雲頂高原10小時專屬包車，配專業司機（中英文）<br>不含景點門票、纜車票及個人消費", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 從接客時間起10小時 <br>● 餐食: 自理 <br>● 機場接送: 需額外付費" },
        "ko": { "description": "겐팅 하이랜드 10시간 전용 차량, 전문 기사 동행 (중국어/영어)<br>입장권, 케이블카 티켓 및 개인 경비 불포함", "remark": "● 픽업: 쿠알라룸푸르 지정 장소만 <br>● 예약: 출발 전날 오후 8시까지 <br>● 시간: 픽업 시간부터 10시간 <br>● 식사: 개별 준비 <br>● 공항 픽업: 추가 요금" }
    },
    "KLIA => Kuala Lumpur": {
        "id": { "description": "Layanan penjemputan dari Bandara Kuala Lumpur ke pusat kota Kuala Lumpur", "remark": "● Waktu tunggu gratis: 90 menit berdasarkan waktu kedatangan pesawat <br>● Batas waktu pemesanan: Minimum 6 jam <br>● Kapasitas: Pastikan jumlah penumpang dan bagasi sesuai batas <br>● Biaya tambahan: Titik pemberhentian ekstra dan layanan tambahan" },
        "ms": { "description": "Perkhidmatan pengambilan dari Lapangan Terbang Kuala Lumpur ke pusat bandar Kuala Lumpur", "remark": "● Masa menunggu percuma: 90 minit berdasarkan masa ketibaan penerbangan <br>● Masa pesanan minimum: 6 jam <br>● Had kapasiti: Pastikan jumlah penumpang dan bagasi mematuhi had muatan <br>● Bayaran tambahan: Perhentian ekstra atau perkhidmatan tambahan" },
        "en": { "description": "Airport pickup service from Kuala Lumpur Airport to Kuala Lumpur city center", "remark": "● Free waiting time: 90 minutes based on actual flight arrival <br>● Minimum booking time: 6 hours <br>● Capacity limits: Ensure passenger and luggage meet vehicle limits <br>● Additional charges: Extra stops or additional services" },
        "vi": { "description": "Dịch vụ đón sân bay Kuala Lumpur về trung tâm thành phố Kuala Lumpur", "remark": "● Thời gian chờ miễn phí: 90 phút tính từ giờ máy bay hạ cánh thực tế <br>● Thời gian đặt dịch vụ tối thiểu: 6 giờ <br>● Giới hạn tải trọng: Đảm bảo số lượng người và hành lý phù hợp <br>● Phụ phí: Các điểm dừng bổ sung hoặc dịch vụ gia tăng" },
        "ru": { "description": "Трансфер из аэропорта Куала-Лумпур в центр города Куала-Лумпур", "remark": "● Бесплатное ожидание: 90 минут с момента фактического прибытия рейса <br>● Минимальное время заказа: 6 часов <br>● Ограничения по вместимости: Количество пассажиров и багажа должно соответствовать лимитам <br>● Дополнительная плата: За дополнительные остановки и услуги" },
        "th": { "description": "บริการรับจากสนามบินกัวลาลัมเปอร์ไปยังใจกลางเมืองกัวลาลัมเปอร์", "remark": "● เวลารอฟรี: 90 นาที นับจากเวลาเครื่องบินลงจอดจริง <br>● เวลาสั่งซื้อขั้นต่ำ: 6 ชั่วโมง <br>● ข้อจำกัดความจุ: ตรวจสอบจำนวนผู้โดยสารและสัมภาระให้เป็นไปตามข้อจำกัด <br>● ค่าใช้จ่ายเพิ่มเติม: จุดแวะพักเพิ่มเติมหรือบริการเสริม" },
        "ja": { "description": "クアラルンプール空港からクアラルンプール市内への送迎サービス", "remark": "● 無料待機時間: 90分 フライト実際の到着時間に基づく <br>● 最低注文時間: 6時間 <br>● 積載制限: 人数と荷物が積載制限を満たしていることを確認 <br>● 追加料金: 追加停車地点やその他の追加サービス" },
        "zh-CN": { "description": "吉隆坡机场接机服务至吉隆坡市区", "remark": "● 免费等待时间: 90分钟 以航班实际到达时间为准 <br>● 最短下单时间: 6小时 <br>● 承载限制: 确认人数与行李符合车辆限制 <br>● 额外费用: 额外停靠点及其他附加服务" },
        "zh-TW": { "description": "吉隆坡機場接機服務至吉隆坡市區", "remark": "● 免費等待時間: 90分鐘 依航班實際到達時間為準 <br>● 最短下單時間: 6小時 <br>● 承載限制: 確認人數與行李符合車輛限制 <br>● 額外費用: 額外停靠點及其他附加服務" },
        "ko": { "description": "쿠알라룸푸르 공항에서 쿠알라룸푸르 시내로의 픽업 서비스", "remark": "● 무료 대기 시간: 90분 주문 시간 기준 <br>● 최소 주문 시간: 6시간 <br>● 적재 제한: 탑승 인원 및 수하물이 차량 제한을 초과하지 않도록 확인 <br>● 추가 요금: 추가 정차 또는 기타 부가 서비스" }
    },
    "Kuala Lumpur => KLIA": {
        "id": { "description": "Layanan antar dari pusat kota Kuala Lumpur ke Bandara Kuala Lumpur", "remark": "● Waktu tunggu gratis: 30 menit sesuai waktu pesanan <br>● Batas waktu pemesanan: Minimum 6 jam <br>● Kapasitas: Pastikan jumlah penumpang dan bagasi sesuai batas <br>● Biaya tambahan: Titik pemberhentian ekstra dan layanan tambahan" },
        "ms": { "description": "Perkhidmatan penghantaran dari pusat bandar Kuala Lumpur ke Lapangan Terbang Kuala Lumpur", "remark": "● Masa menunggu percuma: 30 minit mengikut masa tempahan <br>● Masa pesanan minimum: 6 jam <br>● Had kapasiti: Pastikan jumlah penumpang dan bagasi mematuhi had muatan <br>● Bayaran tambahan: Perhentian ekstra atau perkhidmatan tambahan" },
        "en": { "description": "Drop-off service from Kuala Lumpur city center to Kuala Lumpur Airport", "remark": "● Free waiting time: 30 minutes based on order schedule <br>● Minimum booking time: 6 hours <br>● Capacity limits: Ensure passenger and luggage meet vehicle limits <br>● Additional charges: Extra stops or additional services" },
        "vi": { "description": "Dịch vụ đưa tiễn từ trung tâm thành phố Kuala Lumpur đến sân bay Kuala Lumpur", "remark": "● Thời gian chờ miễn phí: 30 phút tính từ giờ đặt xe <br>● Thời gian đặt xe tối thiểu: 6 giờ <br>● Giới hạn tải trọng: Kiểm tra số lượng người và hành lý trước khi đặt <br>● Phụ phí: Các điểm dừng bổ sung hoặc dịch vụ đi kèm" },
        "ru": { "description": "Трансфер из центра Куала-Лумпура в аэропорт Куала-Лумпура", "remark": "● Бесплатное ожидание: 30 минут согласно времени заказа <br>● Минимальное время заказа: 6 часов <br>● Ограничения по вместимости: Количество пассажиров и багажа должно соответствовать лимитам <br>● Дополнительная плата: За дополнительные остановки и услуги" },
        "th": { "description": "บริการส่งจากใจกลางเมืองกัวลาลัมเปอร์ไปยังสนามบินกัวลาลัมเปอร์", "remark": "● เวลารอฟรี: 30 นาที ตามเวลาที่สั่งจอง <br>● เวลาสั่งซื้อขั้นต่ำ: 6 ชั่วโมง <br>● ข้อจำกัดความจุ: ตรวจสอบจำนวนผู้โดยสารและสัมภาระให้เป็นไปตามข้อจำกัด <br>● ค่าใช้จ่ายเพิ่มเติม: จุดแวะพักเพิ่มเติมหรือบริการเสริม" },
        "ja": { "description": "クアラルンプール市内からクアラルンプール空港への送迎サービス", "remark": "● 無料待機時間: 30分 ご注文時間による <br>● 最低注文時間: 6時間 <br>● 積載制限: 人数・荷物が積載制限内であることを確認 <br>● 追加料金: 追加停車地点やその他の追加サービス" },
        "zh-CN": { "description": "吉隆坡市区送机服务至吉隆坡机场", "remark": "● 免费等待时间: 30分钟 根据订单时间 <br>● 最短下单时间: 6小时 <br>● 承载限制: 确保人数与行李符合车辆限制 <br>● 额外费用: 额外停靠点及其他附加服务" },
        "zh-TW": { "description": "吉隆坡市區送機服務至吉隆坡機場", "remark": "● 免費等待時間: 30分鐘 依訂單時間 <br>● 最短下單時間: 6小時 <br>● 承載限制: 確認人數與行李符合車輛限制 <br>● 額外費用: 額外停靠點及其他附加服務" },
        "ko": { "description": "쿠알라룸푸르 시내에서 쿠알라룸푸르 공항으로의 픽업 서비스", "remark": "● 무료 대기 시간: 30분 주문 시간 기준 <br>● 최소 주문 시간: 6시간 <br>● 적재 제한: 탑승 인원 및 수하물이 차량 제한을 초과하지 않도록 확인 <br>● 추가 요금: 추가 정차 또는 기타 부가 서비스" }
    },
    "Klang Valley <=> ONE WORLD HOTEL Petaling Jaya": {
        "id": { "description": "Layanan transfer antara Klang Valley dan ONE WORLD HOTEL Petaling Jaya", "remark": "● Zona penjemputan: Area Klang Valley (Kuala Lumpur, Petaling Jaya, Shah Alam, Subang) <br>● Lokasi tujuan: ONE WORLD HOTEL Petaling Jaya <br>● Waktu tunggu gratis: 30 menit untuk drop-off <br>● Pemesanan: Minimum 3 jam sebelumnya" },
        "ms": { "description": "Perkhidmatan pemindahan antara Klang Valley dan ONE WORLD HOTEL Petaling Jaya", "remark": "● Zon pengambilan: Kawasan Klang Valley (Kuala Lumpur, Petaling Jaya, Shah Alam, Subang) <br>● Lokasi destinasi: ONE WORLD HOTEL Petaling Jaya <br>● Masa menunggu percuma: 30 minit untuk drop-off <br>● Tempahan: Minimum 3 jam sebelumnya" },
        "en": { "description": "Transfer service between Klang Valley and ONE WORLD HOTEL Petaling Jaya", "remark": "● Pickup zone: Klang Valley area (Kuala Lumpur, Petaling Jaya, Shah Alam, Subang) <br>● Destination: ONE WORLD HOTEL Petaling Jaya <br>● Free waiting time: 30 minutes for drop-off <br>● Booking: Minimum 3 hours in advance" },
        "vi": { "description": "Dịch vụ đưa đón giữa Klang Valley và ONE WORLD HOTEL Petaling Jaya", "remark": "● Khu vực đón: Vùng Klang Valley (Kuala Lumpur, Petaling Jaya, Shah Alam, Subang) <br>● Điểm đến: ONE WORLD HOTEL Petaling Jaya <br>● Thời gian chờ miễn phí: 30 phút cho việc thả khách <br>● Đặt xe: Tối thiểu 3 giờ trước" },
        "ru": { "description": "Трансферная служба между Кланг Валли и ONE WORLD HOTEL Петалинг Джая", "remark": "● Зона посадки: Район Кланг Валли (Куала-Лумпур, Петалинг Джая, Шах Алам, Субанг) <br>● Пункт назначения: ONE WORLD HOTEL Петалинг Джая <br>● Бесплатное время ожидания: 30 минут для высадки <br>● Бронирование: Минимум за 3 часа" },
        "th": { "description": "บริการรับส่งระหว่างคลังวัลเลย์และ ONE WORLD HOTEL เปตาลิงจายา", "remark": "● โซนรับส่ง: พื้นที่คลังวัลเลย์ (กัวลาลัมเปอร์, เปตาลิงจายา, ชาห์อาลัม, สุบัง) <br>● จุดหมาย: ONE WORLD HOTEL เปตาลิงจายา <br>● เวลารอฟรี: 30 นาทีสำหรับการส่ง <br>● การจอง: ขั้นต่ำ 3 ชั่วโมงล่วงหน้า" },
        "ja": { "description": "クランバレーとONE WORLD HOTEL ペタリンジャヤ間の送迎サービス", "remark": "● 送迎エリア: クランバレー地域（クアラルンプール、ペタリンジャヤ、シャーアラム、スバン） <br>● 目的地: ONE WORLD HOTEL ペタリンジャヤ <br>● 無料待機時間: 送り30分 <br>● 予約: 最低3時間前" },
        "zh-CN": { "description": "巴生谷与八打灵再也ONE WORLD HOTEL之间的接送服务", "remark": "● 接送区域: 巴生谷地区（吉隆坡、八打灵再也、莎阿南、梳邦） <br>● 目的地: 八打灵再也ONE WORLD HOTEL <br>● 免费等待时间: 送客30分钟 <br>● 预订: 最少提前3小时" },
        "zh-TW": { "description": "巴生谷與八打靈再也ONE WORLD HOTEL之間的接送服務", "remark": "● 接送區域: 巴生谷地區（吉隆坡、八打靈再也、莎阿南、梳邦） <br>● 目的地: 八打靈再也ONE WORLD HOTEL <br>● 免費等待時間: 送客30分鐘 <br>● 預訂: 最少提前3小時" },
        "ko": { "description": "클랑 밸리와 페탈링 자야 ONE WORLD HOTEL 간 픽업 서비스", "remark": "● 픽업 지역: 클랑 밸리 지역 (쿠알라룸푸르, 페탈링 자야, 샤 알람, 수방) <br>● 목적지: 페탈링 자야 ONE WORLD HOTEL <br>● 무료 대기 시간: 드롭오프 30분 <br>● 예약: 최소 3시간 전" }
    },
    "KLIA => Genting Highland": {
        "id": { "description": "Layanan transfer dari Bandara Kuala Lumpur ke Genting Highland", "remark": "● Waktu tunggu gratis: 90 menit berdasarkan waktu kedatangan pesawat <br>● Batas waktu pemesanan: Minimum 6 jam <br>● Kapasitas: Pastikan jumlah penumpang dan bagasi sesuai batas kendaraan <br>● Biaya tambahan: Pemberhentian tambahan atau layanan ekstra lainnya" },
        "ms": { "description": "Perkhidmatan pemindahan dari Lapangan Terbang Kuala Lumpur ke Genting Highland", "remark": "● Masa menunggu percuma: 90 minit berdasarkan masa ketibaan penerbangan <br>● Masa pesanan minimum: 6 jam <br>● Had kapasiti: Pastikan jumlah penumpang dan bagasi tidak melebihi had kenderaan <br>● Caj tambahan: Perhentian tambahan atau perkhidmatan sampingan lain" },
        "en": { "description": "Transfer service from Kuala Lumpur Airport to Genting Highland", "remark": "● Free waiting time: 90 minutes based on actual flight arrival time <br>● Minimum booking time: 6 hours <br>● Capacity limit: Ensure passenger count and luggage do not exceed vehicle limits <br>● Additional charges: Extra stops or other additional services" },
        "vi": { "description": "Dịch vụ đưa đón từ Sân bay Kuala Lumpur đến Genting Highlands", "remark": "● Thời gian chờ miễn phí: 90 phút dựa trên giờ đến thực tế của chuyến bay <br>● Thời gian đặt tối thiểu: 6 tiếng <br>● Giới hạn sức chứa: Đảm bảo số lượng hành khách và hành lý không vượt quá giới hạn xe <br>● Phí bổ sung: Điểm dừng thêm hoặc các dịch vụ bổ sung khác" },
        "ru": { "description": "Трансферная служба из аэропорта Куала-Лумпур в Гентинг Хайленд", "remark": "● Бесплатное время ожидания: 90 минут от фактического времени прибытия рейса <br>● Минимальное время бронирования: 6 часов <br>● Ограничение вместимости: Убедитесь, что количество пассажиров и багажа не превышает лимиты автомобиля <br>● Дополнительные сборы: Дополнительные остановки или другие дополнительные услуги" },
        "th": { "description": "บริการรับส่งจากสนามบินกัวลาลัมเปอร์ไปเก็นติ้งไฮแลนด์", "remark": "● เวลารอฟรี: 90 นาทีนับจากเวลาเครื่องบินมาถึงจริง <br>● เวลาจองขั้นต่ำ: 6 ชั่วโมง <br>● ขีดจำกัดความจุ: ให้แน่ใจว่าจำนวนผู้โดยสารและสัมภาระไม่เกินขีดจำกัดของรถ <br>● ค่าบริการเพิ่มเติม: การหยุดเพิ่มเติมหรือบริการอื่นๆ" },
        "ja": { "description": "クアラルンプール空港からゲンティンハイランドへの送迎サービス", "remark": "● 無料待機時間: フライト実際到着時刻から90分 <br>● 最小予約時間: 6時間 <br>● 積載制限: 乗客数・荷物が車両制限を超えないよう確認 <br>● 追加料金: 追加停車またはその他付加サービス" },
        "zh-CN": { "description": "吉隆坡机场至云顶高原接送服务", "remark": "● 免费等待时间: 以航班实际到达时间起90分钟 <br>● 最少订单时间: 6小时 <br>● 载客限制: 确保乘客人数及行李不超过车辆限制 <br>● 额外费用: 额外停靠或其他附加服务" },
        "zh-TW": { "description": "吉隆坡機場至雲頂高原接送服務", "remark": "● 免費等待時間: 以航班實際到達時間起90分鐘 <br>● 最少訂單時間: 6小時 <br>● 載客限制: 確保乘客人數及行李不超過車輛限制 <br>● 額外費用: 額外停靠或其他附加服務" },
        "ko": { "description": "쿠알라룸푸르 공항에서 겐팅 하이랜드로의 픽업 서비스", "remark": "● 무료 대기 시간: 90분 항공편 실제 도착 시간 기준 <br>● 최소 주문 시간: 6시간 <br>● 적재 제한: 탑승 인원 및 수하물이 차량 제한을 초과하지 않도록 확인 <br>● 추가 요금: 추가 정차 또는 기타 부가 서비스" }
    },
    "KLIA => Melaka": {
        "id": { "description": "Layanan transfer dari Bandara Kuala Lumpur ke Melaka", "remark": "● Waktu tunggu gratis: 90 menit berdasarkan waktu kedatangan pesawat <br>● Batas waktu pemesanan: Minimum 6 jam <br>● Kapasitas: Pastikan jumlah penumpang dan bagasi sesuai batas kendaraan <br>● Biaya tambahan: Pemberhentian tambahan atau layanan ekstra lainnya" },
        "ms": { "description": "Perkhidmatan pemindahan dari Lapangan Terbang Kuala Lumpur ke Melaka", "remark": "● Masa menunggu percuma: 90 minit berdasarkan masa ketibaan penerbangan <br>● Masa pesanan minimum: 6 jam <br>● Had kapasiti: Pastikan jumlah penumpang dan bagasi tidak melebihi had kenderaan <br>● Caj tambahan: Perhentian tambahan atau perkhidmatan sampingan lain" },
        "en": { "description": "Transfer service from Kuala Lumpur Airport to Melaka", "remark": "● Free waiting time: 90 minutes based on actual flight arrival time <br>● Minimum booking time: 6 hours <br>● Capacity limit: Ensure passenger count and luggage do not exceed vehicle limits <br>● Additional charges: Extra stops or other additional services" },
        "vi": { "description": "Dịch vụ đưa đón từ Sân bay Kuala Lumpur đến Melaka", "remark": "● Thời gian chờ miễn phí: 90 phút dựa trên giờ đến thực tế của chuyến bay <br>● Thời gian đặt tối thiểu: 6 tiếng <br>● Giới hạn sức chứa: Đảm bảo số lượng hành khách và hành lý không vượt quá giới hạn xe <br>● Phí bổ sung: Điểm dừng thêm hoặc các dịch vụ bổ sung khác" },
        "ru": { "description": "Трансферная служба из аэропорта Куала-Лумпур в Мелаку", "remark": "● Бесплатное время ожидания: 90 минут от фактического времени прибытия рейса <br>● Минимальное время бронирования: 6 часов <br>● Ограничение вместимости: Убедитесь, что количество пассажиров и багажа не превышает лимиты автомобиля <br>● Дополнительные сборы: Дополнительные остановки или другие дополнительные услуги" },
        "th": { "description": "บริการรับส่งจากสนามบินกัวลาลัมเปอร์ไปมะละกา", "remark": "● เวลารอฟรี: 90 นาทีนับจากเวลาเครื่องบินมาถึงจริง <br>● เวลาจองขั้นต่ำ: 6 ชั่วโมง <br>● ขีดจำกัดความจุ: ให้แน่ใจว่าจำนวนผู้โดยสารและสัมภาระไม่เกินขีดจำกัดของรถ <br>● ค่าบริการเพิ่มเติม: การหยุดเพิ่มเติมหรือบริการอื่นๆ" },
        "ja": { "description": "クアラルンプール空港からマラッカへの送迎サービス", "remark": "● 無料待機時間: フライト実際到着時刻から90分 <br>● 最小予約時間: 6時間 <br>● 積載制限: 乗客数・荷物が車両制限を超えないよう確認 <br>● 追加料金: 追加停車またはその他付加サービス" },
        "zh-CN": { "description": "吉隆坡机场至马六甲接送服务", "remark": "● 免费等待时间: 以航班实际到达时间起90分钟 <br>● 最少订单时间: 6小时 <br>● 载客限制: 确保乘客人数及行李不超过车辆限制 <br>● 额外费用: 额外停靠或其他附加服务" },
        "zh-TW": { "description": "吉隆坡機場至馬六甲接送服務", "remark": "● 免費等待時間: 以航班實際到達時間起90分鐘 <br>● 最少訂單時間: 6小時 <br>● 載客限制: 確保乘客人數及行李不超過車輛限制 <br>● 額外費用: 額外停靠或其他附加服務" },
        "ko": { "description": "쿠알라룸푸르 공항에서 말라카로의 픽업 서비스", "remark": "● 무료 대기 시간: 90분 항공편 실제 도착 시간 기준 <br>● 최소 주문 시간: 6시간 <br>● 적재 제한: 탑승 인원 및 수하물이 차량 제한을 초과하지 않도록 확인 <br>● 추가 요금: 추가 정차 또는 기타 부가 서비스" }
    },
    "Genting Highland => KLIA": {
        "id": { "description": "Layanan transfer dari Genting Highland ke Bandara Kuala Lumpur", "remark": "● Lokasi penjemputan: Hotel atau lokasi tertentu di Genting Highland <br>● Waktu perjalanan: Sekitar 1.5-2 jam tergantung lalu lintas <br>● Pemesanan: Minimum 6 jam sebelum keberangkatan <br>● Kapasitas: Pastikan jumlah penumpang dan bagasi sesuai batas kendaraan" },
        "ms": { "description": "Perkhidmatan pemindahan dari Genting Highland ke Lapangan Terbang Kuala Lumpur", "remark": "● Lokasi pengambilan: Hotel atau lokasi tertentu di Genting Highland <br>● Masa perjalanan: Sekitar 1.5-2 jam bergantung kepada lalu lintas <br>● Tempahan: Minimum 6 jam sebelum berlepas <br>● Had kapasiti: Pastikan jumlah penumpang dan bagasi tidak melebihi had kenderaan" },
        "en": { "description": "Transfer service from Genting Highland to Kuala Lumpur Airport", "remark": "● Pick-up location: Hotels or designated locations in Genting Highland <br>● Travel time: Approximately 1.5-2 hours depending on traffic <br>● Booking: Minimum 6 hours before departure <br>● Capacity limit: Ensure passenger count and luggage do not exceed vehicle limits" },
        "vi": { "description": "Dịch vụ đưa đón từ Genting Highland đến Sân bay Kuala Lumpur", "remark": "● Điểm đón: Khách sạn hoặc địa điểm được chỉ định tại Genting Highland <br>● Thời gian di chuyển: Khoảng 1.5-2 giờ tùy thuộc vào giao thông <br>● Đặt xe: Tối thiểu 6 tiếng trước khi khởi hành <br>● Giới hạn sức chứa: Đảm bảo số lượng hành khách và hành lý không vượt quá giới hạn xe" },
        "ru": { "description": "Трансферная служба из Гентинг Хайленд в аэропорт Куала-Лумпур", "remark": "● Место посадки: Отели или указанные места в Гентинг Хайленд <br>● Время в пути: Примерно 1.5-2 часа в зависимости от трафика <br>● Бронирование: Минимум за 6 часов до отправления <br>● Ограничение вместимости: Убедитесь, что количество пассажиров и багажа не превышает лимиты автомобиля" },
        "th": { "description": "บริการรับส่งจากเก็นติ้งไฮแลนด์ไปสนามบินกัวลาลัมเปอร์", "remark": "● จุดรับ: โรงแรมหรือสถานที่ที่กำหนดในเก็นติ้งไฮแลนด์ <br>● เวลาเดินทาง: ประมาณ 1.5-2 ชั่วโมงขึ้นอยู่กับการจราจร <br>● การจอง: ขั้นต่ำ 6 ชั่วโมงก่อนออกเดินทาง <br>● ขีดจำกัดความจุ: ให้แน่ใจว่าจำนวนผู้โดยสารและสัมภาระไม่เกินขีดจำกัดของรถ" },
        "ja": { "description": "ゲンティンハイランドからクアラルンプール空港への送迎サービス", "remark": "● 乗車場所: ゲンティンハイランド内のホテルまたは指定場所 <br>● 所要時間: 交通状況により約1.5-2時間 <br>● 予約: 出発6時間前まで <br>● 積載制限: 乗客数・荷物が車両制限を超えないよう確認" },
        "zh-CN": { "description": "云顶高原至吉隆坡机场接送服务", "remark": "● 接客地点: 云顶高原酒店或指定地点 <br>● 行程时间: 根据交通状况约1.5-2小时 <br>● 预订: 出发前最少6小时 <br>● 载客限制: 确保乘客人数及行李不超过车辆限制" },
        "zh-TW": { "description": "雲頂高原至吉隆坡機場接送服務", "remark": "● 接客地點: 雲頂高原酒店或指定地點 <br>● 行程時間: 根據交通狀況約1.5-2小時 <br>● 預訂: 出發前最少6小時 <br>● 載客限制: 確保乘客人數及行李不超過車輛限制" },
        "ko": { "description": "겐팅 하이랜드에서 쿠알라룸푸르 공항으로의 픽업 서비스", "remark": "● 픽업 위치: 겐팅 하이랜드 호텔 또는 지정 장소 <br>● 이동 시간: 교통 상황에 따라 약 1.5-2시간 <br>● 예약: 출발 최소 6시간 전 <br>● 적재 제한: 탑승 인원 및 수하물이 차량 제한을 초과하지 않도록 확인" }
    },
    "Melaka => KLIA": {
        "id": { "description": "Layanan transfer dari Melaka ke Bandara Kuala Lumpur", "remark": "● Lokasi penjemputan: Hotel atau lokasi tertentu di Melaka <br>● Waktu perjalanan: Sekitar 2-2.5 jam tergantung lalu lintas <br>● Pemesanan: Minimum 6 jam sebelum keberangkatan <br>● Kapasitas: Pastikan jumlah penumpang dan bagasi sesuai batas kendaraan" },
        "ms": { "description": "Perkhidmatan pemindahan dari Melaka ke Lapangan Terbang Kuala Lumpur", "remark": "● Lokasi pengambilan: Hotel atau lokasi tertentu di Melaka <br>● Masa perjalanan: Sekitar 2-2.5 jam bergantung kepada lalu lintas <br>● Tempahan: Minimum 6 jam sebelum berlepas <br>● Had kapasiti: Pastikan jumlah penumpang dan bagasi tidak melebihi had kenderaan" },
        "en": { "description": "Transfer service from Melaka to Kuala Lumpur Airport", "remark": "● Pick-up location: Hotels or designated locations in Melaka <br>● Travel time: Approximately 2-2.5 hours depending on traffic <br>● Booking: Minimum 6 hours before departure <br>● Capacity limit: Ensure passenger count and luggage do not exceed vehicle limits" },
        "vi": { "description": "Dịch vụ đưa đón từ Melaka đến Sân bay Kuala Lumpur", "remark": "● Điểm đón: Khách sạn hoặc địa điểm được chỉ định tại Melaka <br>● Thời gian di chuyển: Khoảng 2-2.5 giờ tùy thuộc vào giao thông <br>● Đặt xe: Tối thiểu 6 tiếng trước khi khởi hành <br>● Giới hạn sức chứa: Đảm bảo số lượng hành khách và hành lý không vượt quá giới hạn xe" },
        "ru": { "description": "Трансферная служба из Мелаки в аэропорт Куала-Лумпур", "remark": "● Место посадки: Отели или указанные места в Мелаке <br>● Время в пути: Примерно 2-2.5 часа в зависимости от трафика <br>● Бронирование: Минимум за 6 часов до отправления <br>● Ограничение вместимости: Убедитесь, что количество пассажиров и багажа не превышает лимиты автомобиля" },
        "th": { "description": "บริการรับส่งจากมะละกาไปสนามบินกัวลาลัมเปอร์", "remark": "● จุดรับ: โรงแรมหรือสถานที่ที่กำหนดในมะละกา <br>● เวลาเดินทาง: ประมาณ 2-2.5 ชั่วโมงขึ้นอยู่กับการจราจร <br>● การจอง: ขั้นต่ำ 6 ชั่วโมงก่อนออกเดินทาง <br>● ขีดจำกัดความจุ: ให้แน่ใจว่าจำนวนผู้โดยสารและสัมภาระไม่เกินขีดจำกัดของรถ" },
        "ja": { "description": "マラッカからクアラルンプール空港への送迎サービス", "remark": "● 乗車場所: マラッカ内のホテルまたは指定場所 <br>● 所要時間: 交通状況により約2-2.5時間 <br>● 予約: 出発6時間前まで <br>● 積載制限: 乗客数・荷物が車両制限を超えないよう確認" },
        "zh-CN": { "description": "马六甲至吉隆坡机场接送服务", "remark": "● 接客地点: 马六甲酒店或指定地点 <br>● 行程时间: 根据交通状况约2-2.5小时 <br>● 预订: 出发前最少6小时 <br>● 载客限制: 确保乘客人数及行李不超过车辆限制" },
        "zh-TW": { "description": "馬六甲至吉隆坡機場接送服務", "remark": "● 接客地點: 馬六甲酒店或指定地點 <br>● 行程時間: 根據交通狀況約2-2.5小時 <br>● 預訂: 出發前最少6小時 <br>● 載客限制: 確保乘客人數及行李不超過車輛限制" },
        "ko": { "description": "말라카에서 쿠알라룸푸르 공항으로의 픽업 서비스", "remark": "● 픽업 위치: 말라카 호텔 또는 지정 장소 <br>● 이동 시간: 교통 상황에 따라 약 2-2.5시간 <br>● 예약: 출발 최소 6시간 전 <br>● 적재 제한: 탑승 인원 및 수하물이 차량 제한을 초과하지 않도록 확인" }
    },
    "Point to Point Transfer Service (Genting)": {
        "id": { "description": "Layanan antar jemput satu arah antara pusat kota Kuala Lumpur dan Genting Highlands", "remark": "● Waktu tunggu gratis: 15 menit sesuai waktu pesanan <br>● Batas waktu pemesanan: Minimum 6 jam <br>● Kapasitas: Pastikan jumlah penumpang dan bagasi sesuai batas <br>● Biaya tambahan: Titik pemberhentian ekstra dan layanan tambahan <br>●Batasan: Lokasi naik turun hanya terbatas di pusat kota Kuala Lumpur dan Genting Highlands" },
        "ms": { "description": "Perkhidmatan pemindahan sehala antara pusat bandar Kuala Lumpur dan Genting Highlands", "remark": "● Masa menunggu percuma: 15 minit mengikut masa tempahan <br>● Masa pesanan minimum: 6 jam <br>● Had kapasiti: Pastikan jumlah penumpang dan bagasi mematuhi had muatan <br>● Bayaran tambahan: Perhentian ekstra atau perkhidmatan tambahan <br>●Sekatan: Lokasi naik turun terhad kepada pusat bandar Kuala Lumpur dan Genting Highlands sahaja" },
        "en": { "description": "One-way transfer service between Kuala Lumpur city center and Genting Highlands", "remark": "● Free waiting time: 15 minutes based on order schedule <br>● Minimum booking time: 6 hours <br>● Capacity limits: Ensure passenger and luggage meet vehicle limits <br>● Additional charges: Extra stops or additional services <br>●Restrictions: Pick-up and drop-off locations limited to Kuala Lumpur city center and Genting Highlands only" },
        "vi": { "description": "Dịch vụ đưa đón một chiều giữa trung tâm thành phố Kuala Lumpur và Genting Highlands", "remark": "● Thời gian chờ miễn phí: 15 phút tính từ giờ đặt xe <br>● Thời gian đặt xe tối thiểu: 6 giờ <br>● Giới hạn tải trọng: Kiểm tra số lượng người và hành lý trước khi đặt <br>● Phụ phí: Các điểm dừng bổ sung hoặc dịch vụ đi kèm <br>●Hạn chế: Điểm đón và trả khách chỉ giới hạn tại trung tâm thành phố Kuala Lumpur và Genting Highlands" },
        "ru": { "description": "Односторонний трансфер между центром Куала-Лумпура и Гентинг Хайлендс", "remark": "● Бесплатное ожидание: 15 минут согласно времени заказа <br>● Минимальное время заказа: 6 часов <br>● Ограничения по вместимости: Количество пассажиров и багажа должно соответствовать лимитам <br>● Дополнительная плата: За дополнительные остановки и услуги <br>●Ограничения: Места посадки и высадки ограничены только центром Куала-Лумпура и Гентинг Хайлендс" },
        "th": { "description": "บริการรับส่งเที่ยวเดียวระหว่างใจกลางเมืองกัวลาลัมเปอร์และเก็นติ้งไฮแลนด์", "remark": "● เวลารอฟรี: 15 นาที ตามเวลาที่สั่งจอง <br>● เวลาสั่งซื้อขั้นต่ำ: 6 ชั่วโมง <br>● ข้อจำกัดความจุ: ตรวจสอบจำนวนผู้โดยสารและสัมภาระให้เป็นไปตามข้อจำกัด <br>● ค่าใช้จ่ายเพิ่มเติม: จุดแวะพักเพิ่มเติมหรือบริการเสริม <br>●ข้อจำกัด: จุดรับส่งจำกัดเฉพาะใจกลางเมืองกัวลาลัมเปอร์และเก็นติ้งไฮแลนด์เท่านั้น" },
        "ja": { "description": "クアラルンプール市内とゲンティンハイランド間の片道送迎サービス", "remark": "● 無料待機時間: 15分 ご注文時間による <br>● 最低注文時間: 6時間 <br>● 積載制限: 人数・荷物が積載制限内であることを確認 <br>● 追加料金: 追加停車地点やその他の追加サービス <br>●制限: 乗降場所はクアラルンプール市内とゲンティンハイランドのみに限定" },
        "zh-CN": { "description": "吉隆坡市区单程接送云顶高原", "remark": "● 免费等待时间: 15分钟 根据订单时间 <br>● 最短下单时间: 6小时 <br>● 承载限制: 确保人数与行李符合车辆限制 <br>● 额外费用: 额外停靠点及其他附加服务 <br>●限制: 上下车地点仅限吉隆坡市区，云顶高原" },
        "zh-TW": { "description": "吉隆坡市區單程接送雲頂高原", "remark": "● 免費等待時間: 15分鐘 依訂單時間 <br>● 最短下單時間: 6小時 <br>● 承載限制: 確認人數與行李符合車輛限制 <br>● 額外費用: 額外停靠點及其他附加服務 <br>●限制: 上下車地點僅限吉隆坡市區，雲頂高原" },
        "ko": { "description": "쿠알라룸푸르 시내와 겐팅 하이랜드 간 편도 픽업 서비스", "remark": "● 무료 대기 시간: 15분 주문 시간 기준 <br>● 최소 주문 시간: 6시간 <br>● 적재 제한: 탑승 인원 및 수하물이 차량 제한을 초과하지 않도록 확인 <br>● 추가 요금: 추가 정차 또는 기타 부가 서비스 <br>●제한: 승하차 지점은 쿠알라룸푸르 시내와 겐팅 하이랜드로만 제한" }
    },
    "Shuttle From Genting Highland": {
        "id": { "description": "Layanan shuttle dari First World Hotel Genting ke KLCC", "remark": "● Lokasi naik: First World Hotel Genting <br>● Lokasi turun: Stasiun Bus KLCC <br>● Batasan bagasi: Satu ransel per penumpang, koper dikenakan biaya tambahan <br>● Keberangkatan tepat waktu, tidak menunggu keterlambatan" },
        "ms": { "description": "Perkhidmatan shuttle dari First World Hotel Genting ke KLCC", "remark": "● Lokasi naik: First World Hotel Genting <br>● Lokasi turun: Stesen Bas KLCC <br>● Had bagasi: Satu beg galas per penumpang, beg kembara dikenakan caj tambahan <br>● Berlepas tepat pada masa, tidak menunggu yang lewat" },
        "en": { "description": "Shuttle service from First World Hotel Genting to KLCC", "remark": "● Pick-up location: First World Hotel Genting <br>● Drop-off location: KLCC Bus Station <br>● Luggage restriction: One backpack per passenger, suitcases require additional fee <br>● Departure on time, no waiting for latecomers" },
        "vi": { "description": "Dịch vụ xe buýt từ First World Hotel Genting đến KLCC", "remark": "● Điểm đón: First World Hotel Genting <br>● Điểm trả: Bến xe buýt KLCC <br>● Hạn chế hành lý: Một ba lô mỗi hành khách, vali phải trả phí thêm <br>● Khởi hành đúng giờ, không chờ đợi" },
        "ru": { "description": "Шаттл-сервис от First World Hotel Genting до KLCC", "remark": "● Место посадки: First World Hotel Genting <br>● Место высадки: Автобусная станция KLCC <br>● Ограничения багажа: Один рюкзак на пассажира, чемоданы за дополнительную плату <br>● Отправление точно по времени, опоздавших не ждём" },
        "th": { "description": "บริการรถรับส่งจาก First World Hotel Genting ไป KLCC", "remark": "● จุดขึ้น: First World Hotel Genting <br>● จุดลง: สถานีรถบัส KLCC <br>● ข้อจำกัดสัมภาระ: กระเป๋าเป้หนึ่งใบต่อผู้โดยสาร กระเป๋าเดินทางต้องเสียค่าใช้จ่ายเพิ่มเติม <br>● ออกเดินทางตรงเวลา ไม่รอผู้มาสาย" },
        "ja": { "description": "ゲンティン・ファーストワールドホテルからKLCCへのシャトルサービス", "remark": "● 乗車場所: ゲンティン・ファーストワールドホテル <br>● 降車場所: KLCCバスステーション <br>● 荷物制限: お一人様バックパック1個まで、スーツケースは追加料金 <br>● 定刻発車、遅刻者はお待ちしません" },
        "zh-CN": { "description": "云顶第一大酒店至KLCC班车服务", "remark": "● 上车地点: 云顶第一大酒店 <br>● 下车地点: KLCC巴士站 <br>● 行李限制: 每位乘客限带一个背包，行李箱需额外付费 <br>● 准时发车，过时不候" },
        "zh-TW": { "description": "雲頂第一大酒店至KLCC班車服務", "remark": "● 上車地點: 雲頂第一大酒店 <br>● 下車地點: KLCC巴士站 <br>● 行李限制: 每位乘客限帶一個背包，行李箱需額外付費 <br>● 準時發車，過時不候" },
        "ko": { "description": "겐팅 퍼스트 월드 호텔에서 KLCC까지의 셔틀 서비스", "remark": "● 승차 지점: 겐팅 퍼스트 월드 호텔 <br>● 하차 지점: KLCC 버스 정류장 <br>● 수하물 제한: 승객당 백팩 1개, 캐리어는 추가 요금 <br>● 정시 출발, 지각자 대기 불가" }
    },
    "Shuttle To Genting Highland": {
        "id": { "description": "Layanan shuttle dari First World Hotel Genting ke Genting Highlands", "remark": "● Lokasi naik: First World Hotel Genting <br>● Lokasi turun: Genting Highlands <br>● Batasan bagasi: Satu ransel per penumpang, koper dikenakan biaya tambahan <br>● Keberangkatan tepat waktu, tidak menunggu keterlambatan" },
        "ms": { "description": "Perkhidmatan shuttle dari First World Hotel Genting ke Genting Highlands", "remark": "● Lokasi naik: First World Hotel Genting <br>● Lokasi turun: Genting Highlands <br>● Had bagasi: Satu beg galas per penumpang, beg kembara dikenakan caj tambahan <br>● Berlepas tepat pada masa, tidak menunggu yang lewat" },
        "en": { "description": "Shuttle service from First World Hotel Genting to Genting Highlands", "remark": "● Pick-up location: First World Hotel Genting <br>● Drop-off location: Genting Highlands <br>● Luggage restriction: One backpack per passenger, suitcases require additional fee <br>● Departure on time, no waiting for latecomers" },
        "vi": { "description": "Dịch vụ xe buýt từ First World Hotel Genting đến Genting Highlands", "remark": "● Điểm đón: First World Hotel Genting <br>● Điểm trả: Genting Highlands <br>● Hạn chế hành lý: Một ba lô mỗi hành khách, vali phải trả phí thêm <br>● Khởi hành đúng giờ, không chờ đợi" },
        "ru": { "description": "Шаттл-сервис от First World Hotel Genting до Гентинг Хайлендс", "remark": "● Место посадки: First World Hotel Genting <br>● Место высадки: Гентинг Хайлендс <br>● Ограничения багажа: Один рюкзак на пассажира, чемоданы требуют дополнительной платы <br>● Отправление строго по расписанию, опоздавших не ждут" },
        "th": { "description": "บริการรถรับส่งจากโรงแรมเฟิร์สเวิลด์เจนติ้งถึงเก็นติ้งไฮแลนด์", "remark": "● จุดรับ: โรงแรมเฟิร์สเวิลด์เจนติ้ง <br>● จุดส่ง: เก็นติ้งไฮแลนด์ <br>● ข้อจำกัดกระเป๋าเดินทาง: หนึ่งกระเป๋าเป้ต่อผู้โดยสารหนึ่งคน กระเป๋าเดินทางต้องชำระค่าบริการเพิ่ม <br>● ออกเดินทางตรงเวลา ไม่รอผู้มาสาย" },
        "ja": { "description": "ファーストワールドホテル・ゲンティンからゲンティンハイランドへのシャトルサービス", "remark": "● 乗車場所: ファーストワールドホテル・ゲンティン <br>● 降車場所: ゲンティンハイランド <br>● 手荷物制限: 1人1バックパックまで、スーツケースは追加料金 <br>● 時間通りに出発、遅刻者待ちなし" },
        "zh-CN": { "description": "云顶第一大酒店至云顶高原班车服务", "remark": "● 上车地点: 云顶第一大酒店 <br>● 下车地点: 云顶高原 <br>● 行李限制: 每位乘客限带一个背包，行李箱需额外付费 <br>● 准时发车，过时不候" },
        "zh-TW": { "description": "雲頂第一大酒店至雲頂高原班車服務", "remark": "● 上車地點: 雲頂第一大酒店 <br>● 下車地點: 雲頂高原 <br>● 行李限制: 每位乘客限帶一個背包，行李箱需額外付費 <br>● 準時發車，過時不候" },
        "ko": { "description": "퍼스트 월드 호텔 겐팅에서 겐팅 하이랜드까지 셔틀 서비스", "remark": "● 탑승 위치: 퍼스트 월드 호텔 겐팅 <br>● 하차 위치: 겐팅 하이랜드 <br>● 수하물 제한: 승객 1인당 배낭 1개, 여행용 가방은 추가 요금 <br>● 정시 출발, 지각자 기다리지 않음" }
    },
    "Hourly Charter 3 Hour": {
        "id": { "description": "Layanan sewa mobil pribadi 3 jam di kota dengan sopir profesional. Lokasi penjemputan fleksibel", "remark": "● Penjemputan: Terbatas pada lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum pukul 8 malam di malam sebelum keberangkatan <br>● Durasi: Maksimal 3 jam <br>● Makanan: Diatur sendiri <br>● Biaya tambahan: Overtime lebih dari 3 jam & transfer bandara" },
        "ms": { "description": "Perkhidmatan sewa kereta peribadi 3 jam di bandar dengan pemandu profesional. Lokasi pengambilan fleksibel", "remark": "● Pengambilan: Terhad kepada lokasi yang ditetapkan di Kuala Lumpur <br>● Tempahan: Sebelum 8 malam pada malam sebelum berlepas <br>● Tempoh: Maksimum 3 jam <br>● Makanan: Atur sendiri <br>● Caj tambahan: Lebih masa melebihi 3 jam & pemindahan lapangan terbang" },
        "en": { "description": "3-hour private charter service in the city with professional driver. Flexible pickup locations", "remark": "● Pick-up: Limited to designated Kuala Lumpur locations <br>● Booking: Before 8 PM the night before departure <br>● Duration: Maximum 3 hours <br>● Meals: Self-arranged <br>● Additional charges: Overtime beyond 3 hours & airport transfers" },
        "vi": { "description": "Dịch vụ thuê xe riêng 3 giờ trong thành phố với tài xế chuyên nghiệp. Điểm đón linh hoạt", "remark": "● Đón: Chỉ giới hạn tại các địa điểm được chỉ định ở Kuala Lumpur <br>● Đặt xe: Trước 8 giờ tối của đêm trước khi khởi hành <br>● Thời gian: Tối đa 3 giờ <br>● Bữa ăn: Tự túc <br>● Phí phụ thu: Vượt quá 3 giờ & đưa đón sân bay" },
        "ru": { "description": "3-часовой сервис частного автомобиля в городе с профессиональным водителем. Гибкие места посадки", "remark": "● Посадка: Ограничено указанными местами в Куала-Лумпуре <br>● Бронирование: До 8 вечера накануне отправления <br>● Продолжительность: Максимум 3 часа <br>● Питание: Самостоятельно <br>● Дополнительная плата: Сверхурочные свыше 3 часов и трансферы в аэропорт" },
        "th": { "description": "บริการเช่ารถส่วนตัว 3 ชั่วโมงในเมืองพร้อมคนขับมืออาชีพ จุดรับส่งยืดหยุ่น", "remark": "● การรับส่ง: จำกัดเฉพาะสถานที่ที่กำหนดในกัวลาลัมเปอร์ <br>● การจอง: ก่อน 8 โมงเย็นของคืนก่อนออกเดินทาง <br>● ระยะเวลา: สูงสุด 3 ชั่วโมง <br>● อาหาร: จัดการเอง <br>● ค่าใช้จ่ายเพิ่มเติม: ค่าล่วงเวลาเกิน 3 ชั่วโมง และการรับส่งสนามบิน" },
        "ja": { "description": "市内3時間プライベートチャーターサービス、プロドライバー付き。柔軟な送迎場所", "remark": "● 送迎: クアラルンプール指定場所限定 <br>● 予約: 出発前夜の8時まで <br>● 時間: 最大3時間 <br>● 食事: 各自手配 <br>● 追加料金: 3時間超過分・空港送迎" },
        "zh-CN": { "description": "市区3小时私人包车服务，配专业司机。灵活接送地点", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 最多3小时 <br>● 餐食: 自理 <br>● 额外费用: 超3小时 & 机场接送" },
        "zh-TW": { "description": "市區3小時私人包車服務，配專業司機。靈活接送地點", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 最多3小時 <br>● 餐食: 自理 <br>● 額外費用: 超3小時 & 機場接送" },
        "ko": { "description": "시내 3시간 전용 차량 서비스, 전문 기사 배정. 유연한 픽업 위치", "remark": "● 픽업: 쿠알라룸푸르 지정 장소로 제한 <br>● 예약: 출발 전날 저녁 8시 이전 <br>● 시간: 최대 3시간 <br>● 식사: 개별 준비 <br>● 추가 요금: 3시간 초과 및 공항 픽업" }
    },
    "Hourly Charter 8 Hour": {
        "id": { "description": "Layanan sewa mobil pribadi 8 jam di kota dengan sopir profesional. Jadwal fleksibel", "remark": "● Penjemputan: Terbatas pada lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum pukul 8 malam di malam sebelum keberangkatan <br>● Durasi: Maksimal 8 jam <br>● Jarak: Maksimal 50 km <br>● Biaya termasuk: Parkir, Tol <br>● Biaya tambahan: Biaya lebih masa & biaya lebih jarak <br>● Makanan: Atur sendiri" },
        "ms": { "description": "Perkhidmatan sewa kereta peribadi 8 jam di bandar dengan pemandu profesional. Jadual fleksibel", "remark": "● Pengambilan: Terhad kepada lokasi yang ditetapkan di Kuala Lumpur <br>● Tempahan: Sebelum 8 malam pada malam sebelum berlepas <br>● Tempoh: Maksimum 8 jam <br>● Jarak: Maksimum 50 km <br>● Bayaran termasuk: Bayaran parkir, Bayaran tol <br>● Bayaran tambahan: Bayaran lebih masa & bayaran lebih jarak <br>● Makanan: Atur sendiri" },
        "en": { "description": "8-hour private charter service in the city with professional driver. Flexible schedule", "remark": "● Pick-up: Limited to designated Kuala Lumpur locations <br>● Booking: Before 8 PM the night before departure <br>● Duration: Maximum 8 hours <br>● Distance: Maximum 50 km <br>● Included fees: Parking, Tolls <br>● Additional charges: Overtime fees & excess distance charges <br>● Meals: Self-arranged" },
        "vi": { "description": "Dịch vụ thuê xe riêng 8 giờ trong thành phố với tài xế chuyên nghiệp. Lịch trình linh hoạt", "remark": "● Đón khách: Chỉ giới hạn tại các địa điểm chỉ định tại Kuala Lumpur <br>● Đặt dịch vụ: Trước 8 giờ tối của đêm trước khi khởi hành <br>● Thời gian: Tối đa 8 giờ <br>● Khoảng cách: Tối đa 50 km <br>● Phí bao gồm: Phí đỗ xe, Phí đường <br>● Phí phụ thu: Phí vượt quá thời gian & phí vượt quá khoảng cách <br>● Ăn uống: Tự túc" },
        "ru": { "description": "8-часовой сервис частного автомобиля в городе с профессиональным водителем. Гибкий график", "remark": "● Посадка: Ограничено указанными местами в Куала-Лумпуре <br>● Бронирование: До 8 вечера накануне отправления <br>● Продолжительность: Максимум 8 часов <br>● Расстояние: Максимум 50 км <br>● Стоимость включает: Парковка, дорожные сборы <br>● Дополнительная плата: Сверхурочные и за превышение расстояния <br>● Питание: Самостоятельно" },
        "th": { "description": "บริการเช่ารถส่วนตัว 8 ชั่วโมงในเมืองพร้อมคนขับมืออาชีพ ตารางเวลายืดหยุ่น", "remark": "● การรับส่ง: จำกัดเฉพาะสถานที่ที่กำหนดในกัวลาลัมเปอร์ <br>● การจอง: ก่อน 8 โมงเย็นของคืนก่อนออกเดินทาง <br>● ระยะเวลา: สูงสุด 8 ชั่วโมง <br>● ระยะทาง: สูงสุด 50 กิโลเมตร <br>● ค่าใช้จ่ายรวม: ค่าจอดรถ, ค่าผ่านทาง <br>● ค่าใช้จ่ายเพิ่มเติม: ค่าล่วงเวลา & ค่าระยะทางเกิน <br>● อาหาร: จัดการเอง" },
        "ja": { "description": "市内8時間プライベートチャーターサービス、プロドライバー付き。柔軟なスケジュール", "remark": "● 送迎: クアラルンプール指定場所限定 <br>● 予約: 出発前夜の8時まで <br>● 時間: 最大8時間 <br>● 距離: 最大50km <br>● 料金に含まれるもの: 駐車場代、高速代 <br>● 追加料金: 時間超過料金・距離超過料金 <br>● 食事: 各自手配" },
        "zh-CN": { "description": "市区8小时私人包车服务，配专业司机。全日灵活行程安排", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 最多8小时 <br>● 里程: 限制50公里内 <br>● 费用包含: 停车费、过路费 <br>● 额外费用: 超时费因车型而异，超里程费用另计 <br>● 餐食: 自理" },
        "zh-TW": { "description": "市區8小時私人包車服務，配專業司機。全日靈活行程安排", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 最多8小時 <br>● 里程: 限制50公里內 <br>● 費用包含: 停車費、過路費 <br>● 額外費用: 超時費因車型而異，超里程費用另計 <br>● 餐食: 自理" },
        "ko": { "description": "시내 8시간 전용 차량 서비스, 전문 기사 배정. 하루 유연한 일정", "remark": "● 픽업: 쿠알라룸푸르 지정 장소만 <br>● 예약: 출발 전날 저녁 8시까지 <br>● 시간: 최대 8시간 <br>● 거리: 최대 50km 제한 <br>● 비용 포함: 주차비, 과금 포함 <br>● 추가 비용: 초과 시간 비용은 차량 유형에 따라 다름, 초과 거리 비용 별도 <br>● 식사: 자율 준비" }
    },
    "Kuala Selangor Private Charter (12 Hours)": {
        "id": { "description": "Tour eksklusif 12 jam ke Kuala Selangor dengan sopir profesional (Mandarin/Inggris)<br>Tidak termasuk tiket masuk dan pengeluaran pribadi", "remark": "● Penjemputan: Lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum 20:00 sehari sebelumnya <br>● Durasi: 12 jam dari waktu penjemputan <br>● Makanan: Ditanggung sendiri <br>● Transfer bandara: Biaya tambahan" },
        "ms": { "description": "Pakej eksklusif 12 jam ke Kuala Selangor dengan pemandu profesional (Cina/Inggeris)<br>Tidak termasuk tiket masuk dan perbelanjaan peribadi", "remark": "● Pengambilan: Lokasi terpilih di Kuala Lumpur <br>● Tempahan: Sebelum 8 malam sehari sebelumnya <br>● Tempoh: 12 jam dari masa pengambilan <br>● Makanan: Urusan sendiri <br>● Transfer lapangan terbang: Bayaran tambahan" },
        "en": { "description": "12-hour exclusive charter to Kuala Selangor with professional driver (Chinese/English)<br>Excludes attraction tickets and personal expenses", "remark": "● Pickup: Kuala Lumpur designated locations only <br>● Booking: By 8 PM the day before departure <br>● Duration: 12 hours from pickup time <br>● Meals: Self-catered <br>● Airport transfers: Additional charges" },
        "vi": { "description": "Tour riêng 12 giờ đến Kuala Selangor với tài xế chuyên nghiệp (Trung/Anh)<br>Không bao gồm vé tham quan và chi tiêu cá nhân", "remark": "● Đón khách: Các địa điểm chỉ định tại Kuala Lumpur <br>● Đặt dịch vụ: Trước 20:00 ngày hôm trước <br>● Thời lượng: 12 giờ từ thời điểm đón khách <br>● Ăn uống: Khách tự túc <br>● Đưa đón sân bay: Có phụ phí" },
        "ru": { "description": "12-часовой эксклюзивный тур в Куала-Селангоре с профессиональным водителем (китайский/английский)<br>Входные билеты и личные расходы не включены", "remark": "● Встреча: Указанные места в Куала-Лумпуре <br>● Бронирование: До 20:00 накануне отправления <br>● Продолжительность: 12 часов с момента встречи <br>● Питание: За свой счет <br>● Трансфер в аэропорт: Дополнительная плата" },
        "th": { "description": "ทัวร์เอกชน 12 ชั่วโมงไปกัวลาเซลังงอร์ พร้อมคนขับมืออาชีพ (จีน/อังกฤษ)<br>ไม่รวมค่าเข้าชมและค่าใช้จ่ายส่วนตัว", "remark": "● รับส่ง: เฉพาะจุดที่กำหนดในกัวลาลัมเปอร์<br>● การจอง: ก่อน 20:00 น. ของวันก่อนหน้า <br>● ระยะเวลา: 12 ชั่วโมงนับจากเวลารับ <br>● อาหาร: รับผิดชอบเอง <br>● รับส่งสนามบิน: ค่าใช้จ่ายเพิ่มเติม" },
        "ja": { "description": "クアラセランゴール12時間専用チャーター、プロドライバー付き（中国語/英語）<br>入場料・個人費用別途", "remark": "● 送迎: クアラルンプール指定場所のみ <br>● 予約: 出発前日20時まで <br>● 時間: 乗車時から12時間 <br>● 食事: 各自負担 <br>● 空港送迎: 別途料金" },
        "zh-CN": { "description": "瓜拉雪兰莪12小时专属包车，配专业司机（中英文）<br>不含景点门票及个人消费", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 从接客时间起12小时 <br>● 餐食: 自理 <br>● 机场接送: 需额外付费" },
        "zh-TW": { "description": "瓜拉雪蘭莪12小時專屬包車，配專業司機（中英文）<br>不含景點門票及個人消費", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 從接客時間起12小時 <br>● 餐食: 自理 <br>● 機場接送: 需額外付費" },
        "ko": { "description": "쿠알라 셀랑고르 12시간 전용 차량, 전문 기사 동행 (중국어/영어)<br>입장권 및 개인 경비 불포함", "remark": "● 픽업: 쿠알라룸푸르 지정 장소만 <br>● 예약: 출발 전날 오후 8시까지 <br>● 시간: 픽업 시간부터 12시간 <br>● 식사: 개별 준비 <br>● 공항 픽업: 추가 요금" }
    },
    "Kuala Selangor Hourly Charter (12 Hour)": {
        "id": { "description": "Tour eksklusif 12 jam ke Kuala Selangor dengan sopir profesional (Mandarin/Inggris)<br>Tidak termasuk tiket masuk dan pengeluaran pribadi", "remark": "● Penjemputan: Lokasi tertentu di Kuala Lumpur <br>● Pemesanan: Sebelum 20:00 sehari sebelumnya <br>● Durasi: 12 jam dari waktu penjemputan <br>● Makanan: Ditanggung sendiri <br>● Transfer bandara: Biaya tambahan" },
        "ms": { "description": "Pakej eksklusif 12 jam ke Kuala Selangor dengan pemandu profesional (Cina/Inggeris)<br>Tidak termasuk tiket masuk dan perbelanjaan peribadi", "remark": "● Pengambilan: Lokasi terpilih di Kuala Lumpur <br>● Tempahan: Sebelum 8 malam sehari sebelumnya <br>● Tempoh: 12 jam dari masa pengambilan <br>● Makanan: Urusan sendiri <br>● Transfer lapangan terbang: Bayaran tambahan" },
        "en": { "description": "12-hour exclusive charter to Kuala Selangor with professional driver (Chinese/English)<br>Excludes attraction tickets and personal expenses", "remark": "● Pickup: Kuala Lumpur designated locations only <br>● Booking: By 8 PM the day before departure <br>● Duration: 12 hours from pickup time <br>● Meals: Self-catered <br>● Airport transfers: Additional charges" },
        "vi": { "description": "Tour riêng 12 giờ đến Kuala Selangor với tài xế chuyên nghiệp (Trung/Anh)<br>Không bao gồm vé tham quan và chi tiêu cá nhân", "remark": "● Đón khách: Các địa điểm chỉ định tại Kuala Lumpur <br>● Đặt dịch vụ: Trước 20:00 ngày hôm trước <br>● Thời lượng: 12 giờ từ thời điểm đón khách <br>● Ăn uống: Khách tự túc <br>● Đưa đón sân bay: Có phụ phí" },
        "ru": { "description": "12-часовой эксклюзивный тур в Куала-Селангоре с профессиональным водителем (китайский/английский)<br>Входные билеты и личные расходы не включены", "remark": "● Встреча: Указанные места в Куала-Лумпуре <br>● Бронирование: До 20:00 накануне отправления <br>● Продолжительность: 12 часов с момента встречи <br>● Питание: За свой счет <br>● Трансфер в аэропорт: Дополнительная плата" },
        "th": { "description": "ทัวร์เอกชน 12 ชั่วโมงไปกัวลาเซลังงอร์ พร้อมคนขับมืออาชีพ (จีน/อังกฤษ)<br>ไม่รวมค่าเข้าชมและค่าใช้จ่ายส่วนตัว", "remark": "● รับส่ง: เฉพาะจุดที่กำหนดในกัวลาลัมเปอร์<br>● การจอง: ก่อน 20:00 น. ของวันก่อนหน้า <br>● ระยะเวลา: 12 ชั่วโมงนับจากเวลารับ <br>● อาหาร: รับผิดชอบเอง <br>● รับส่งสนามบิน: ค่าใช้จ่ายเพิ่มเติม" },
        "ja": { "description": "クアラセランゴール12時間専用チャーター、プロドライバー付き（中国語/英語）<br>入場料・個人費用別途", "remark": "● 送迎: クアラルンプール指定場所のみ <br>● 予約: 出発前日20時まで <br>● 時間: 乗車時から12時間 <br>● 食事: 各自負担 <br>● 空港送迎: 別途料金" },
        "zh-CN": { "description": "瓜拉雪兰莪12小时专属包车，配专业司机（中英文）<br>不含景点门票及个人消费", "remark": "● 接送: 仅限吉隆坡指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 从接客时间起12小时 <br>● 餐食: 自理 <br>● 机场接送: 需额外付费" },
        "zh-TW": { "description": "瓜拉雪蘭莪12小時專屬包車，配專業司機（中英文）<br>不含景點門票及個人消費", "remark": "● 接送: 僅限吉隆坡指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 從接客時間起12小時 <br>● 餐食: 自理 <br>● 機場接送: 需額外付費" },
        "ko": { "description": "쿠알라 셀랑고르 12시간 전용 차량, 전문 기사 동행 (중국어/영어)<br>입장권 및 개인 경비 불포함", "remark": "● 픽업: 쿠알라룸푸르 지정 장소만 <br>● 예약: 출발 전날 오후 8시까지 <br>● 시간: 픽업 시간부터 12시간 <br>● 식사: 개별 준비 <br>● 공항 픽업: 추가 요금" }
    },
    "Airport Transfer (Klang, Sg Buloh, Rawang, Semenyih)": {
        "id": { "description": "Layanan transfer bandara untuk wilayah Klang, Sg Buloh, Rawang, dan Semenyih", "remark": "● Penjemputan: Lokasi yang ditentukan di wilayah tersebut <br>● Durasi: Tergantung lalu lintas <br>● Termasuk: Sopir profesional, kendaraan AC" },
        "ms": { "description": "Perkhidmatan pemindahan lapangan terbang untuk kawasan Klang, Sg Buloh, Rawang, dan Semenyih", "remark": "● Pengambilan: Lokasi yang ditetapkan di kawasan tersebut <br>● Tempoh: Bergantung pada trafik <br>● Termasuk: Pemandu profesional, kenderaan berhawa dingin" },
        "en": { "description": "Airport transfer service for Klang, Sg Buloh, Rawang, and Semenyih areas", "remark": "● Pick-up: Designated locations in these areas <br>● Duration: Traffic dependent <br>● Includes: Professional driver, AC vehicle" },
        "vi": { "description": "Dịch vụ đưa đón sân bay cho khu vực Klang, Sg Buloh, Rawang và Semenyih", "remark": "● Đón: Địa điểm chỉ định trong các khu vực này <br>● Thời gian: Phụ thuộc giao thông <br>● Bao gồm: Tài xế chuyên nghiệp, xe có điều hòa" },
        "ru": { "description": "Трансфер в аэропорт для районов Кланг, Сг Було, Раванг и Семених", "remark": "● Посадка: Указанные места в этих районах <br>● Продолжительность: Зависит от трафика <br>● Включено: Профессиональный водитель, кондиционер" },
        "th": { "description": "บริการรับส่งสนามบินสำหรับพื้นที่กลัง, สก.บูโลห์, ราวัง และเซอเมอนีห์", "remark": "● การรับส่ง: สถานที่ที่กำหนดในพื้นที่เหล่านี้ <br>● ระยะเวลา: ขึ้นอยู่กับการจราจร <br>● รวม: คนขับมืออาชีพ, รถปรับอากาศ" },
        "ja": { "description": "クラン、スンガイ・ブロー、ラワン、セメニー地区向け空港送迎サービス", "remark": "● 送迎: 指定地域内の指定場所 <br>● 所要時間: 交通状況による <br>● 含まれるもの: プロドライバー、エアコン車両" },
        "zh-CN": { "description": "巴生、双溪毛糯、万挠、士毛月地区机场接送服务", "remark": "● 接送: 上述地区指定地点 <br>● 时长: 视交通状况而定 <br>● 包含: 专业司机,空调车辆" },
        "ko": { "description": "클랑, 스가이 불로, 라왕, 세메니 지역 공항 픽업 서비스", "remark": "● 픽업: 해당 지역 내 지정 장소 <br>● 소요 시간: 교통 상황에 따라 다름 <br>● 포함: 전문 운전자, 에어컨 차량" },
        "zh-TW": { "description": "巴生、雙溪毛糯、萬撓、士毛月地區機場接送服務", "remark": "● 接送: 上述地區指定地點 <br>● 時長: 視交通狀況而定 <br>● 包含: 專業司機,空調車輛" }
    },
    
    "Firefly Ticket": {
        "id": { "description": "Layanan tiket kapal khusus untuk melihat kunang-kunang", "remark": "● Waktu kumpul: 19:00 di dermaga <br>● Biaya tiket: Khusus untuk pengalaman melihat kunang-kunang <br>● Kebijakan harga: Harga sama untuk dewasa dan anak-anak, gratis untuk anak di bawah 3 tahun <br>● Catatan penting: Memerlukan naik dan turun perahu, tidak disarankan untuk peserta dengan masalah mobilitas <br>● Aturan fotografi: Jangan menggunakan lampu kilat saat melihat kunang-kunang, menangkap kunang-kunang dilarang <br>● Perlindungan: Harap mengambil tindakan pencegahan nyamuk <br>● Durasi: Sekitar 25 menit <br>● Kebijakan pengembalian dana: Pengembalian dana penuh akan diberikan jika terjadi hujan lebat" },
        "ms": { "description": "Perkhidmatan tiket bot khusus untuk menonton kelip-kelip", "remark": "● Masa berkumpul: 19:00 di dermaga <br>● Kandungan tiket: Khusus untuk pengalaman menonton kelip-kelip <br>● Dasar harga: Harga sama untuk dewasa dan kanak-kanak, percuma untuk kanak-kanak bawah 3 tahun <br>● Nota penting: Memerlukan naik dan turun bot, tidak digalakkan untuk peserta yang mempunyai masalah pergerakan <br>● Peraturan fotografi: Jangan menggunakan lampu kilat semasa melihat kelip-kelip, menangkap kelip-kelip adalah dilarang <br>● Langkah perlindungan: Sila ambil langkah pencegahan nyamuk <br>● Tempoh: Kira-kira 25 minit <br>● Dasar bayaran balik: Bayaran balik penuh akan diberikan sekiranya hujan lebat" },
        "en": { "description": "Dedicated boat ticket service for fireflies watching", "remark": "● Gathering time: 19:00 at the pier <br>● Ticket content: Dedicated fireflies watching experience <br>● Pricing policy: Same price for adults and children, free for children under 3 years old <br>● Important notes: Requires boarding and disembarking, not recommended for participants with mobility issues <br>● Photography rules: Do not use flash photography during firefly watching, catching fireflies is prohibited <br>● Protection measures: Please take mosquito protection measures <br>● Duration: Approximately 25 minutes <br>● Refund policy: Full refund will be provided in case of heavy rain" },
        "vi": { "description": "Dịch vụ vé thuyền chuyên dụng để ngắm đom đóm", "remark": "● Thời gian tập trung: 19:00 tại bến cảng <br>● Nội dung vé: Dành riêng cho trải nghiệm ngắm đom đóm <br>● Chính sách giá: Giá như nhau cho người lớn và trẻ em, miễn phí cho trẻ dưới 3 tuổi <br>● Lưu ý quan trọng: Yêu cầu lên xuống thuyền, không khuyến nghị cho người có vấn đề về vận động <br>● Quy tắc chụp ảnh: Không sử dụng đèn flash khi ngắm đom đóm, cấm bắt đom đóm <br>● Biện pháp bảo vệ: Vui lòng áp dụng biện pháp chống muỗi <br>● Thời lượng: Khoảng 25 phút <br>● Chính sách hoàn tiền: Sẽ hoàn lại toàn bộ tiền trong trường hợp mưa lớn" },
        "ru": { "description": "Услуга по продаже специализированных билетов на лодку для наблюдения за светлячками", "remark": "● Время сбора: 19:00 на пирсе <br>● Содержание билета: Специализированный опыт наблюдения за светлячками <br>● Политика ценообразования: Одинаковая цена для взрослых и детей, бесплатно для детей до 3 лет <br>● Важные примечания: Требуется посадка и высадка, не рекомендуется для участников с проблемами мобильности <br>● Правила фотографии: Не использовать вспышку при наблюдении за светлячками, ловить светлячков запрещено <br>● Меры защиты: Пожалуйста, примите меры против комаров <br>● Продолжительность: Примерно 25 минут <br>● Политика возврата: Полный возврат средств будет предоставлен в случае сильного дождя" },
        "th": { "description": "บริการตั๋วเรือเฉพาะสำหรับชมหิ่งห้อย", "remark": "● เวลานัดพบ: 19:00 น. ที่ท่าเรือ <br>● เนื้อหาตั๋ว: เฉพาะสำหรับประสบการณ์ชมหิ่งห้อย <br>● นโยบายราคา: ราคาเดียวกันสำหรับผู้ใหญ่และเด็ก ฟรีสำหรับเด็กอายุต่ำกว่า 3 ปี <br>● หมายเหตุสำคัญ: ต้องมีการขึ้นและลงเรือ ไม่แนะนำสำหรับผู้ที่มีปัญหาด้านการเคลื่อนไหว <br>● กฎการถ่ายภาพ: ห้ามใช้แฟลชขณะชมหิ่งห้อย ห้ามจับหิ่งห้อย <br>● มาตรการป้องกัน: โปรดป้องกันยุง <br>● ระยะเวลา: ประมาณ 25 นาที <br>● นโยบายการคืนเงิน: จะคืนเงินเต็มจำนวนในกรณีฝนตกหนัก" },
        "ja": { "description": "ホタル鑑賞専用ボートチケットサービス", "remark": "● 集合時間: 19:00 桟橋にて <br>● チケット内容: ホタル鑑賞専用体験 <br>● 料金ポリシー: 大人子供同額、3歳未満無料 <br>● 重要事項: 乗船降船が必要、移動に制約のある方にはお勧めしません <br>● 写真規則: ホタル鑑賞時はフラッシュ禁止、ホタル採取禁止 <br>● 保護対策: 蚊対策をお願いします <br>● 所要時間: 約25分 <br>● 返金ポリシー: 大雨の場合は全額返金" },
        "zh-CN": { "description": "萤火虫观赏专用船票服务", "remark": "● 集合时间: 19:00码头集合 <br>● 票务内容: 专门的萤火虫观赏体验 <br>● 价格政策: 成人儿童同价，3岁以下免费 <br>● 重要提示: 需上下船，行动不便者不建议参加 <br>● 摄影规则: 萤火虫观赏时禁止使用闪光灯，禁止捕捉萤火虫 <br>● 防护措施: 请做好防蚊措施 <br>● 时长: 约25分钟 <br>● 退款政策: 如遇大雨将全额退款" },
        "zh-TW": { "description": "螢火蟲觀賞專用船票服務", "remark": "● 集合時間: 19:00碼頭集合 <br>● 票務內容: 專門的螢火蟲觀賞體驗 <br>● 價格政策: 成人兒童同價，3歲以下免費 <br>● 重要提示: 需上下船，行動不便者不建議參加 <br>● 攝影規則: 螢火蟲觀賞時禁止使用閃光燈，禁止捕捉螢火蟲 <br>● 防護措施: 請做好防蚊措施 <br>● 時長: 約25分鐘 <br>● 退款政策: 如遇大雨將全額退款" },
        "ko": { "description": "반딧불이 관찰 전용 보트 티켓 서비스", "remark": "● 집합 시간: 19:00 부두 집합 <br>● 티켓 내용: 반딧불이 관찰 전용 경험 <br>● 가격 정책: 성인과 어린이 동일 가격, 3세 미만 무료 <br>● 중요 사항: 승하선이 필요하며, 이동이 불편한 참가자에게는 권장하지 않음 <br>● 사진 규칙: 반딧불이 관찰 시 플래시 사용 금지, 반딧불이 포획 금지 <br>● 보호 조치: 모기 방지 조치를 취해 주세요 <br>● 소요 시간: 약 25분 <br>● 환불 정책: 폭우 시 전액 환불" },
    },
    "Eagle Feeding Ticket": {
        "id": { "description": "Layanan tiket tur perahu pemberian makan elang Kuala Selangor", "remark": "● Waktu kumpul: 18:15 di dermaga <br>● Waktu pemesanan terakhir: Sebelum jam 15:00 di hari yang sama <br>● Isi tiket: Biaya tur perahu pemberian makan elang <br>● Kebijakan harga: Harga sama untuk dewasa dan anak-anak, gratis untuk anak di bawah 3 tahun <br>● Catatan penting: Memerlukan naik dan turun perahu, tidak disarankan untuk peserta dengan masalah mobilitas <br>● Durasi: Sekitar 25 menit <br>● Kebijakan pengembalian dana: Pengembalian dana penuh akan diberikan jika terjadi hujan lebat" },
        "ms": { "description": "Perkhidmatan tiket bot pemberian makanan helang Kuala Selangor", "remark": "● Masa berkumpul: 18:15 di dermaga <br>● Masa tempahan terkini: Sebelum 15:00 pada hari yang sama <br>● Kandungan tiket: Bayaran bot pemberian makanan helang <br>● Dasar harga: Harga sama untuk dewasa dan kanak-kanak, percuma untuk kanak-kanak bawah 3 tahun <br>● Nota penting: Memerlukan naik dan turun bot, tidak digalakkan untuk peserta yang mempunyai masalah mobiliti <br>● Tempoh: Kira-kira 25 minit <br>● Dasar bayaran balik: Bayaran balik penuh akan diberikan sekiranya hujan lebat" },
        "en": { "description": "Kuala Selangor Eagle feeding boat tour ticket service", "remark": "● Gathering time: 18:15 at the pier <br>● Latest booking time: Before 15:00 on the same day <br>● Ticket content: Eagle boat tour fee <br>● Pricing policy: Same price for adults and children, free for children under 3 years old <br>● Important notes: Requires boarding and disembarking, not recommended for participants with mobility issues <br>● Duration: Approximately 25 minutes <br>● Refund policy: Full refund will be provided in case of heavy rain" },
        "vi": { "description": "Dịch vụ vé thuyền tham quan cho đại bàng ăn tại Kuala Selangor", "remark": "● Thời gian tập trung: 18:15 tại bến cảng <br>● Thời gian đặt vé muộn nhất: Trước 15:00 cùng ngày <br>● Nội dung vé: Phí tham quan thuyền cho đại bàng ăn <br>● Chính sách giá: Giá như nhau cho người lớn và trẻ em, miễn phí cho trẻ dưới 3 tuổi <br>● Lưu ý quan trọng: Cần lên xuống thuyền, không khuyến nghị cho người có vấn đề về vận động <br>● Thời lượng: Khoảng 25 phút <br>● Chính sách hoàn tiền: Hoàn tiền toàn bộ nếu trời mưa to" },
        "ru": { "description": "Услуга по продаже билетов на лодочную экскурсию с кормлением орлов в Куала-Селангоре", "remark": "● Время сбора: 18:15 на пирсе <br>● Последнее время бронирования: До 15:00 в тот же день <br>● Содержание билета: Стоимость лодочной экскурсии с кормлением орлов <br>● Политика ценообразования: Одинаковая цена для взрослых и детей, бесплатно для детей до 3 лет <br>● Важные примечания: Требуется посадка и высадка, не рекомендуется для участников с ограниченной подвижностью <br>● Продолжительность: Примерно 25 минут <br>● Политика возврата: Полный возврат средств будет предоставлен в случае сильного дождя" },
        "th": { "description": "บริการตั๋วทัวร์เรือให้อาหารนกอินทรีย์ที่กัวลาเซลังงอร์", "remark": "● เวลานัดพบ: 18:15 น. ที่ท่าเรือ <br>● เวลาจองล่าสุด: ก่อน 15:00 น. ของวันเดียวกัน <br>● เนื้อหาตั๋ว: ค่าใช้จ่ายทัวร์เรือให้อาหารนกอินทรีย์ <br>● นโยบายราคา: ราคาเดียวกันสำหรับผู้ใหญ่และเด็ก ฟรีสำหรับเด็กอายุต่ำกว่า 3 ปี <br>● ข้อควรทราบ: ต้องมีการขึ้นและลงเรือ ไม่แนะนำสำหรับผู้ที่มีปัญหาด้านการเคลื่อนไหว <br>● ระยะเวลา: ประมาณ 25 นาที <br>● นโยบายการคืนเงิน: จะคืนเงินเต็มจำนวนในกรณีที่ฝนตกหนัก" },
        "ja": { "description": "クアラセランゴールのイーグルフィーディングボートツアーチケットサービス", "remark": "● 集合時間: 18:15 桟橋にて <br>● 最終予約時間: 当日15:00まで <br>● チケット内容: イーグルボートツアー料金 <br>● 料金ポリシー: 大人子供同額、3歳未満無料 <br>● 重要事項: 乗船降船が必要、移動に制約のある方にはお勧めしません <br>● 所要時間: 約25分 <br>● 返金ポリシー: 大雨の場合は全額返金" },
        "zh-CN": { "description": "瓜拉雪兰莪老鹰喂食船游票务服务", "remark": "● 集合时间: 18:15码头集合 <br>● 最迟下单时间: 当天15:00以前 <br>● 票务内容: 老鹰船游费用 <br>● 价格政策: 成人小孩同价，三岁以下免费 <br>● 注意事项: 需要上下船，不建议行动不方便的参与者 <br>● 行程时长: 约25分钟 <br>● 退款政策: 如遇大雨天气将全额退款" },
        "zh-TW": { "description": "瓜拉雪蘭莪老鷹餵食船遊票務服務", "remark": "● 集合時間: 18:15碼頭集合 <br>● 最遲下單時間: 當天15:00以前 <br>● 票務內容: 老鷹船遊費用 <br>● 價格政策: 成人小孩同價，三歲以下免費 <br>● 注意事項: 需要上下船，不建議行動不方便的參與者 <br>● 行程時長: 約25分鐘 <br>● 退款政策: 如遇大雨天氣將全額退款" },
        "ko": { "description": "쿠알라셀랑고르 독수리 먹이주기 보트 투어 티켓 서비스", "remark": "● 집합 시간: 18:15 부두 집합 <br>● 최근 예약 시간: 당일 15:00 이전 <br>● 티켓 내용: 독수리 보트 투어 요금 <br>● 가격 정책: 성인과 어린이 동일 가격, 3세 미만 무료 <br>● 중요 사항: 승하선이 필요하며, 이동이 불편한 참가자에게는 권장하지 않음 <br>● 소요 시간: 약 25분 <br>● 환불 정책: 폭우 시 전액 환불" }
    },
    "Blue Tear Ticket": {
        "id": { "description": "Layanan tiket tur perahu air mata biru Kuala Selangor", "remark": "● Waktu kumpul: 20:00 di dermaga <br>● Waktu pemesanan terakhir: Sebelum jam 15:00 di hari yang sama <br>● Isi tiket: Biaya tur perahu air mata biru <br>● Kebijakan harga: Harga sama untuk dewasa dan anak-anak, gratis untuk anak di bawah 3 tahun <br>● Catatan penting: Memerlukan naik dan turun perahu, tidak disarankan untuk peserta dengan masalah mobilitas <br>● Durasi: Sekitar 25 menit <br>● Kebijakan pengembalian dana: Pengembalian dana penuh akan diberikan jika terjadi hujan lebat <br>● Aturan fotografi: Dilarang menggunakan flash saat menonton air mata biru" },
        "ms": { "description": "Perkhidmatan tiket bot air mata biru Kuala Selangor", "remark": "● Masa berkumpul: 20:00 di dermaga <br>● Masa tempahan terkini: Sebelum 15:00 pada hari yang sama <br>● Kandungan tiket: Bayaran bot air mata biru <br>● Dasar harga: Harga sama untuk dewasa dan kanak-kanak, percuma untuk kanak-kanak bawah 3 tahun <br>● Nota penting: Memerlukan naik dan turun bot, tidak digalakkan untuk peserta yang mempunyai masalah mobiliti <br>● Tempoh: Kira-kira 25 minit <br>● Dasar bayaran balik: Bayaran balik penuh akan diberikan sekiranya hujan lebat <br>● Peraturan fotografi: Dilarang menggunakan flash semasa menonton air mata biru" },
        "en": { "description": "Kuala Selangor Blue tear boat tour ticket service", "remark": "● Gathering time: 20:00 at the pier <br>● Latest booking time: Before 15:00 on the same day <br>● Ticket content: Blue tear boat tour fee <br>● Pricing policy: Same price for adults and children, free for children under 3 years old <br>● Important notes: Requires boarding and disembarking, not recommended for participants with mobility issues <br>● Duration: Approximately 25 minutes <br>● Refund policy: Full refund will be provided in case of heavy rain <br>● Photography rules: Flash photography is prohibited during Blue tear viewing" },
        "vi": { "description": "Dịch vụ vé thuyền tham quan Blue tear tại Kuala Selangor", "remark": "● Thời gian tập trung: 20:00 tại bến cảng <br>● Thời gian đặt vé muộn nhất: Trước 15:00 cùng ngày <br>● Nội dung vé: Phí tham quan thuyền Blue tear <br>● Chính sách giá: Giá như nhau cho người lớn và trẻ em, miễn phí cho trẻ dưới 3 tuổi <br>● Lưu ý quan trọng: Yêu cầu lên xuống thuyền, không khuyến nghị cho người có vấn đề về vận động <br>● Thời lượng: Khoảng 25 phút <br>● Chính sách hoàn tiền: Sẽ hoàn lại toàn bộ tiền trong trường hợp mưa lớn <br>● Quy tắc chụp ảnh: Cấm sử dụng đèn flash khi xem Blue tear" },
        "ru": { "description": "Услуга по продаже билетов на лодочную экскурсию Blue tear в Куала-Селангоре", "remark": "● Время сбора: 20:00 на пирсе <br>● Последнее время бронирования: До 15:00 в тот же день <br>● Содержание билета: Стоимость лодочной экскурсии Blue tear <br>● Политика ценообразования: Одинаковая цена для взрослых и детей, бесплатно для детей до 3 лет <br>● Важные примечания: Требуется посадка и высадка, не рекомендуется для участников с проблемами мобильности <br>● Продолжительность: Примерно 25 минут <br>● Политика возврата: Полный возврат средств в случае сильного дождя <br>● Правила фотосъемки: Запрещена съемка со вспышкой во время просмотра Blue tear" },
        "th": { "description": "บริการตั๋วทัวร์เรือ Blue tear กัวลาเซลังงอร์", "remark": "● เวลานัดพบ: 20:00 น. ที่ท่าเรือ <br>● เวลาจองล่าสุด: ก่อน 15:00 น. ของวันเดียวกัน <br>● เนื้อหาตั๋ว: ค่าทัวร์เรือ Blue tear <br>● นโยบายราคา: ราคาเดียวกันสำหรับผู้ใหญ่และเด็ก ฟรีสำหรับเด็กอายุต่ำกว่า 3 ปี <br>● หมายเหตุสำคัญ: ต้องมีการขึ้นและลงเรือ ไม่แนะนำสำหรับผู้ที่มีปัญหาด้านการเคลื่อนไหว <br>● ระยะเวลา: ประมาณ 25 นาที <br>● นโยบายการคืนเงิน: จะคืนเงินเต็มจำนวนในกรณีฝนตกหนัก <br>● กฎการถ่ายภาพ: ห้ามใช้แฟลชขณะชม Blue tear" },
        "ja": { "description": "クアラセランゴールBlue tearボートツアーチケットサービス", "remark": "● 集合時間: 20:00 桟橋にて <br>● 最終予約時間: 当日15時まで <br>● チケット内容: Blue tearボートツアー料金 <br>● 料金ポリシー: 大人子供同額、3歳未満無料 <br>● 重要事項: 乗船降船が必要、移動に制約のある方には不向き <br>● 所要時間: 約25分 <br>● 返金ポリシー: 大雨の場合は全額返金 <br>● 写真規則: Blue tear鑑賞時のフラッシュ撮影は禁止" },
        "zh-CN": { "description": "瓜拉雪兰莪蓝眼泪观赏船票服务", "remark": "● 集合时间: 20:00码头集合 <br>● 最晚预订时间: 当天15:00前 <br>● 票务内容: 蓝眼泪船游览费 <br>● 价格政策: 成人儿童同价，3岁以下免费 <br>● 重要提示: 需上下船，行动不便者不建议参加 <br>● 时长: 约25分钟 <br>● 退款政策: 如遇大雨将全额退款 <br>● 摄影规则: 观赏蓝眼泪时禁止使用闪光灯" },
        "ko": { "description": "쿠알라셀랑고르 블루 티어 보트 투어 티켓 서비스", "remark": "● 집합 시간: 20:00 부두 집합 <br>● 최근 예약 시간: 당일 15:00 이전 <br>● 티켓 내용: 블루 티어 보트 투어 요금 <br>● 가격 정책: 성인과 어린이 동일 가격, 3세 미만 무료 <br>● 중요 사항: 승하선이 필요하며, 이동이 불편한 참가자에게는 권장하지 않음 <br>● 소요 시간: 약 25분 <br>● 환불 정책: 폭우 시 전액 환불 <br>● 사진 규칙: 블루 티어 관찰 시 플래시 사용 금지" },
        "zh-TW": { "description": "瓜拉雪蘭莪藍眼淚觀賞船票服務", "remark": "● 集合時間: 20:00碼頭集合 <br>● 最晚預訂時間: 當天15:00前 <br>● 票務內容: 藍眼淚船遊覽費 <br>● 價格政策: 成人兒童同價，3歲以下免費 <br>● 重要提示: 需上下船，行動不便者不建議參加 <br>● 時長: 約25分鐘 <br>● 退款政策: 如遇大雨將全額退款 <br>● 攝影規則: 觀賞藍眼淚時禁止使用閃光燈" },
    },
        "Sky Mirror": {
        "id": { "description": "Pengalaman Sky Mirror Beach dengan transfer perahu pulang-pergi", "remark": "● Waktu pemesanan terakhir: Sebelum jam 20:00 dua hari sebelumnya <br>● Biaya termasuk: Biaya perahu pulang-pergi Sky Mirror, penggunaan properti, layanan fotografer (peralatan non-profesional) <br>● Kebijakan harga: Harga sama untuk dewasa dan anak-anak, gratis untuk anak di bawah 3 tahun <br>● Catatan penting: Memerlukan naik dan turun perahu, tidak disarankan untuk peserta dengan masalah mobilitas <br>● Persyaratan khusus: Diperlukan berjalan di air di Sky Mirror, harap siapkan sandal pengganti <br>● Durasi: Sekitar 150 menit <br>● Kebijakan pengembalian dana: Pengembalian dana penuh akan diberikan jika terjadi hujan lebat" },
        "ms": { "description": "Pengalaman Sky Mirror Pantai dengan pemindahan bot pergi-balik", "remark": "● Masa tempahan terkini: Sebelum 20:00 dua hari sebelumnya <br>● Bayaran termasuk: Bayaran bot pergi-balik Sky Mirror, penggunaan prop, perkhidmatan jurugambar (peralatan bukan profesional) <br>● Dasar harga: Harga sama untuk dewasa dan kanak-kanak, percuma untuk kanak-kanak bawah 3 tahun <br>● Nota penting: Memerlukan naik dan turun bot, tidak digalakkan untuk peserta yang mempunyai masalah mobiliti <br>● Keperluan khas: Merendam diperlukan di Sky Mirror, sila sediakan selipar pengganti <br>● Tempoh: Kira-kira 150 minit <br>● Dasar bayaran balik: Bayaran balik penuh akan diberikan sekiranya hujan lebat" },
        "en": { "description": "Beach Sky Mirror experience with round-trip boat transfer", "remark": "● Latest booking time: Before 20:00 two days in advance <br>● Fee includes: Sky Mirror round-trip boat fee, prop usage, photographer service (non-professional equipment) <br>● Pricing policy: Same price for adults and children, free for children under 3 years old <br>● Important notes: Requires boarding and disembarking, not recommended for participants with mobility issues <br>● Special requirements: Wading is required at Sky Mirror, please prepare replacement slippers <br>● Duration: Approximately 150 minutes <br>● Refund policy: Full refund will be provided in case of heavy rain" },
        "vi": { "description": "Trải nghiệm Sky Mirror Bãi biển với dịch vụ thuyền đưa đón khứ hồi", "remark": "● Thời gian đặt vé muộn nhất: Trước 20:00 hai ngày trước <br>● Phí bao gồm: Phí thuyền khứ hồi Sky Mirror, sử dụng đạo cụ, dịch vụ chụp ảnh (thiết bị không chuyên) <br>● Chính sách giá: Giá như nhau cho người lớn và trẻ em, miễn phí cho trẻ dưới 3 tuổi <br>● Lưu ý quan trọng: Cần lên xuống thuyền, không khuyến nghị cho người có vấn đề về vận động <br>● Yêu cầu đặc biệt: Cần lội nước tại Sky Mirror, vui lòng chuẩn bị dép thay thế <br>● Thời lượng: Khoảng 150 phút <br>● Chính sách hoàn tiền: Hoàn tiền toàn bộ nếu trời mưa to" },
        "ru": { "description": "Опыт Sky Mirror на пляже с трансфером на лодке туда и обратно", "remark": "● Последнее время бронирования: До 20:00 за два дня до <br>● Включено: Стоимость лодки туда-обратно Sky Mirror, использование реквизита, услуги фотографа (непрофессиональное оборудование) <br>● Политика ценообразования: Одинаковая цена для взрослых и детей, бесплатно для детей до 3 лет <br>● Важные примечания: Требуется посадка и высадка, не рекомендуется для участников с проблемами мобильности <br>● Особые требования: Требуется ходьба по воде в Sky Mirror, пожалуйста, подготовьте сменные тапочки <br>● Продолжительность: Примерно 150 минут <br>● Политика возврата: Полный возврат средств будет предоставлен в случае сильного дождя" },
        "th": { "description": "ประสบการณ์ Sky Mirror ที่หาดพร้อมบริการเรือรับส่ง", "remark": "● เวลาจองล่าสุด: ก่อน 20:00 น. สองวันล่วงหน้า <br>● ค่าบริการรวม: ค่าเรือไปกลับ Sky Mirror การใช้อุปกรณ์ บริการช่างภาพ (อุปกรณ์ไม่มืออาชีพ) <br>● นโยบายราคา: ราคาเดียวกันสำหรับผู้ใหญ่และเด็ก ฟรีสำหรับเด็กอายุต่ำกว่า 3 ปี <br>● หมายเหตุสำคัญ: ต้องมีการขึ้นและลงเรือ ไม่แนะนำสำหรับผู้ที่มีปัญหาด้านการเคลื่อนไหว <br>● ข้อกำหนดพิเศษ: ต้องลุยน้ำที่ Sky Mirror โปรดเตรียมรองเท้าแตะสำรอง <br>● ระยะเวลา: ประมาณ 150 นาที <br>● นโยบายการคืนเงิน: จะคืนเงินเต็มจำนวนในกรณีฝนตกหนัก" },
        "ja": { "description": "ビーチスカイミラー体験（往復ボート送迎付き）", "remark": "● 最終予約時間: 2日前20時まで <br>● 料金に含まれるもの: スカイミラー往復ボート料金、小道具使用、撮影サービス（非プロ機材） <br>● 料金ポリシー: 大人子供同額、3歳未満無料 <br>● 重要事項: 乗船降船が必要、移動に制約のある方にはお勧めしません <br>● 特別な要件: スカイミラーでは水に入る必要があります、替えのサンダルをご準備ください <br>● 所要時間: 約150分 <br>● 返金ポリシー: 大雨の場合は全額返金" },
        "zh-CN": { "description": "海滩天空之镜体验含往返船接送", "remark": "● 最晚预订时间: 提前两天20:00前 <br>● 费用包含: 天空之镜往返船费、道具使用、摄影师服务（非专业设备） <br>● 价格政策: 成人儿童同价，3岁以下免费 <br>● 重要提示: 需上下船，行动不便者不建议参加 <br>● 特殊要求: 天空之镜需涉水，请准备替换拖鞋 <br>● 时长: 约150分钟 <br>● 退款政策: 如遇大雨将全额退款" },
        "ko": { "description": "바다 하늘 거울 경험 (왕복 보트 탑승 포함)", "remark": "● 최근 예약 시간: 2일 전 20:00 이전 <br>● 포함 비용: 하늘 거울 왕복 보트 요금, 소품 사용, 사진가 서비스 (전문 장비 아님) <br>● 가격 정책: 성인과 어린이 동일 가격, 3세 미만 무료 <br>● 중요 사항: 승하선이 필요하며, 이동이 불편한 참가자에게는 권장하지 않음 <br>● 특별 요구 사항: 하늘 거울에서 물에 들어가야 하며, 대체 타오르개를 준비해주세요 <br>● 소요 시간: 약 150분 <br>● 환불 정책: 폭우 시 전액 환불" },
        "zh-TW": { "description": "海灘天空之鏡體驗含往返船接送", "remark": "● 最晚預訂時間: 提前兩天20:00前 <br>● 費用包含: 天空之鏡往返船費、道具使用、攝影師服務（非專業設備） <br>● 價格政策: 成人兒童同價，3歲以下免費 <br>● 重要提示: 需上下船，行動不便者不建議參加 <br>● 特殊要求: 天空之鏡需涉水，請準備替換拖鞋 <br>● 時長: 約150分鐘 <br>● 退款政策: 如遇大雨將全額退款" },
    },
    // Start of Selection
    "Eagle Feeding, Fireflies and Blue tear Ticket": {
        "id": { "description": "Layanan tiket tur perahu komprehensif Kuala Selangor termasuk pemberian makan elang, melihat kunang-kunang dan pengalaman blue tear", "remark": "● Waktu kumpul: 18:15 di dermaga <br>● Waktu pemesanan terakhir: Sebelum jam 15:00 di hari yang sama <br>● Isi tiket: Biaya tur perahu komprehensif termasuk pemberian makan elang, melihat kunang-kunang dan pengalaman blue tear <br>● Kebijakan harga: Harga sama untuk dewasa dan anak-anak, gratis untuk anak di bawah 3 tahun <br>● Catatan penting: Memerlukan naik dan turun perahu, tidak disarankan untuk peserta dengan masalah mobilitas <br>● Aturan fotografi: Jangan menggunakan lampu kilat saat melihat kunang-kunang, menangkap kunang-kunang dilarang <br>● Perlindungan: Harap mengambil tindakan pencegahan nyamuk <br>● Durasi: Sekitar 75 menit (25 menit per pengalaman) <br>● Kebijakan pengembalian dana: Pengembalian dana penuh akan diberikan jika terjadi hujan lebat <br>● Urutan pengalaman: Tur biasanya mencakup ketiga pengalaman dalam satu perjalanan perahu komprehensif" },
        "ms": { "description": "Perkhidmatan tiket pelancongan bot komprehensif Kuala Selangor termasuk pemberian makanan helang, melihat kelip-kelip dan pengalaman blue tear", "remark": "● Masa berkumpul: 18:15 di dermaga <br>● Masa tempahan terkini: Sebelum 15:00 pada hari yang sama <br>● Kandungan tiket: Bayaran pelancongan bot komprehensif termasuk pemberian makanan helang, melihat kelip-kelip dan pengalaman blue tear <br>● Dasar harga: Harga sama untuk dewasa dan kanak-kanak, percuma untuk kanak-kanak bawah 3 tahun <br>● Nota penting: Memerlukan naik dan turun bot, tidak digalakkan untuk peserta yang mempunyai masalah pergerakan <br>● Peraturan fotografi: Jangan menggunakan lampu kilat semasa melihat kelip-kelip, menangkap kelip-kelip adalah dilarang <br>● Langkah perlindungan: Sila ambil langkah pencegahan nyamuk <br>● Tempoh: Kira-kira 75 minit (25 minit setiap pengalaman) <br>● Dasar bayaran balik: Bayaran balik penuh akan diberikan sekiranya hujan lebat <br>● Urutan pengalaman: Pelancongan biasanya termasuk ketiga-tiga pengalaman dalam satu perjalanan bot komprehensif" },
        "en": { "description": "Kuala Selangor comprehensive boat tour ticket service including Eagle Feeding, Fireflies watching and Blue tear experience", "remark": "● Gathering time: 18:15 at the pier <br>● Latest booking time: Before 15:00 on the same day <br>● Ticket content: Comprehensive boat tour fee including Eagle feeding, Fireflies watching and Blue tear experience <br>● Pricing policy: Same price for adults and children, free for children under 3 years old <br>● Important notes: Requires boarding and disembarking, not recommended for participants with mobility issues <br>● Photography rules: Do not use flash photography during firefly watching, catching fireflies is prohibited <br>● Protection measures: Please take mosquito protection measures <br>● Duration: Approximately 75 minutes (25 minutes per experience) <br>● Refund policy: Full refund will be provided in case of heavy rain <br>● Experience sequence: Tours typically include all three experiences in one comprehensive boat journey" },
        "vi": { "description": "Dịch vụ vé thuyền tham quan tổng hợp Kuala Selangor bao gồm cho đại bàng ăn, ngắm đom đóm và trải nghiệm blue tear", "remark": "● Thời gian tập trung: 18:15 tại bến cảng <br>● Thời gian đặt vé muộn nhất: Trước 15:00 cùng ngày <br>● Nội dung vé: Phí tham quan thuyền tổng hợp bao gồm cho đại bàng ăn, ngắm đom đóm và trải nghiệm blue tear <br>● Chính sách giá: Giá như nhau cho người lớn và trẻ em, miễn phí cho trẻ dưới 3 tuổi <br>● Lưu ý quan trọng: Yêu cầu lên xuống thuyền, không khuyến nghị cho người có vấn đề về vận động <br>● Quy tắc chụp ảnh: Không sử dụng đèn flash khi ngắm đom đóm, cấm bắt đom đóm <br>● Biện pháp bảo vệ: Vui lòng áp dụng biện pháp chống muỗi <br>● Thời lượng: Khoảng 75 phút (25 phút mỗi trải nghiệm) <br>● Chính sách hoàn tiền: Sẽ hoàn lại toàn bộ tiền trong trường hợp mưa lớn <br>● Trình tự trải nghiệm: Các chuyến tham quan thường bao gồm cả ba trải nghiệm trong một hành trình thuyền tổng hợp" },
        "ru": { "description": "Услуга по продаже билетов на комплексную лодочную экскурсию в Куала-Селангоре, включая кормление орлов, наблюдение за светлячками и опыт blue tear", "remark": "● Время сбора: 18:15 на пирсе <br>● Последнее время бронирования: До 15:00 в тот же день <br>● Содержание билета: Стоимость комплексной лодочной экскурсии, включая кормление орлов, наблюдение за светлячками и опыт blue tear <br>● Политика ценообразования: Одинаковая цена для взрослых и детей, бесплатно для детей до 3 лет <br>● Важные примечания: Требуется посадка и высадка, не рекомендуется для участников с проблемами мобильности <br>● Правила фотографии: Не использовать вспышку при наблюдении за светлячками, ловить светлячков запрещено <br>● Меры защиты: Пожалуйста, примите меры против комаров <br>● Продолжительность: Примерно 75 минут (25 минут на каждый опыт) <br>● Политика возврата: Полный возврат средств будет предоставлен в случае сильного дождя <br>● Последовательность опыта: Экскурсии обычно включают все три опыта в одно комплексное лодочное путешествие" },
        "th": { "description": "บริการตั๋วทัวร์เรือแบบครบวงจรที่กัวลาเซลังงอร์ รวมถึงการให้อาหารนกอินทรีย์ ชมหิ่งห้อย และประสบการณ์ blue tear", "remark": "● เวลานัดพบ: 18:15 น. ที่ท่าเรือ <br>● เวลาจองล่าสุด: ก่อน 15:00 น. ของวันเดียวกัน <br>● เนื้อหาตั๋ว: ค่าใช้จ่ายทัวร์เรือแบบครบวงจร รวมถึงการให้อาหารนกอินทรีย์ ชมหิ่งห้อย และประสบการณ์ blue tear <br>● นโยบายราคา: ราคาเดียวกันสำหรับผู้ใหญ่และเด็ก ฟรีสำหรับเด็กอายุต่ำกว่า 3 ปี <br>● หมายเหตุสำคัญ: ต้องมีการขึ้นและลงเรือ ไม่แนะนำสำหรับผู้ที่มีปัญหาด้านการเคลื่อนไหว <br>● กฎการถ่ายภาพ: ห้ามใช้แฟลชขณะชมหิ่งห้อย ห้ามจับหิ่งห้อย <br>● มาตรการป้องกัน: โปรดป้องกันยุง <br>● ระยะเวลา: ประมาณ 75 นาที (25 นาทีต่อประสบการณ์) <br>● นโยบายการคืนเงิน: จะคืนเงินเต็มจำนวนในกรณีฝนตกหนัก <br>● ลำดับประสบการณ์: ทัวร์มักรวมทั้งสามประสบการณ์ในการเดินทางเรือแบบครบวงจรหนึ่งครั้ง" },
        "ja": { "description": "クアラセランゴール総合ボートツアーチケットサービス（イーグルフィーディング、ホタル鑑賞、ブルーティア体験含む）", "remark": "● 集合時間: 18:15 桟橋にて <br>● 最終予約時間: 当日15:00まで <br>● チケット内容: イーグルフィーディング、ホタル鑑賞、ブルーティア体験を含む総合ボートツアー料金 <br>● 料金ポリシー: 大人子供同額、3歳未満無料 <br>● 重要事項: 乗船降船が必要、移動に制約のある方にはお勧めしません <br>● 写真規則: ホタル鑑賞時はフラッシュ禁止、ホタル採取禁止 <br>● 保護対策: 蚊対策をお願いします <br>● 所要時間: 約75分（各体験25分） <br>● 返金ポリシー: 大雨の場合は全額返金 <br>● 体験順序: ツアーでは通常1回の総合ボート旅行で3つの体験すべてを含みます" },
        "zh-CN": { "description": "瓜拉雪兰莪综合船游票务服务（含老鹰喂食、萤火虫观赏和蓝眼泪体验）", "remark": "● 集合时间: 18:15码头集合 <br>● 最晚预订时间: 当天15:00前 <br>● 票务内容: 包含老鹰喂食、萤火虫观赏和蓝眼泪体验的综合船游费用 <br>● 价格政策: 成人儿童同价，3岁以下免费 <br>● 重要提示: 需上下船，行动不便者不建议参加 <br>● 摄影规则: 萤火虫观赏时禁止使用闪光灯，禁止捕捉萤火虫 <br>● 防护措施: 请做好防蚊措施 <br>● 时长: 约75分钟（每个体验25分钟） <br>● 退款政策: 如遇大雨将全额退款 <br>● 体验顺序: 游览通常在一次综合船游中包括所有三个体验" },
        "zh-TW": { "description": "瓜拉雪蘭莪綜合船遊票務服務（含老鷹餵食、螢火蟲觀賞和藍眼淚體驗）", "remark": "● 集合時間: 18:15碼頭集合 <br>● 最晚預訂時間: 當天15:00前 <br>● 票務內容: 包含老鷹餵食、螢火蟲觀賞和藍眼淚體驗的綜合船遊費用 <br>● 價格政策: 成人兒童同價，3歲以下免費 <br>● 重要提示: 需上下船，行動不便者不建議參加 <br>● 攝影規則: 螢火蟲觀賞時禁止使用閃光燈，禁止捕捉螢火蟲 <br>● 防護措施: 請做好防蚊措施 <br>● 時長: 約75分鐘（每個體驗25分鐘） <br>● 退款政策: 如遇大雨將全額退款 <br>● 體驗順序: 遊覽通常在一次綜合船遊中包括所有三個體驗" },
        "ko": { "description": "쿠알라셀랑고르 종합 보트 투어 티켓 서비스 (독수리 먹이주기, 반딧불이 관찰 및 블루 티어 경험 포함)", "remark": "● 집합 시간: 18:15 부두 집합 <br>● 최근 예약 시간: 당일 15:00 이전 <br>● 티켓 내용: 독수리 보트 투어 요금, 반딧불이 관찰 및 블루 티어 경험을 포함한 종합 보트 투어 요금 <br>● 가격 정책: 성인과 어린이 동일 가격, 3세 미만 무료 <br>● 중요 사항: 승하선이 필요하며, 이동이 불편한 참가자에게는 권장하지 않음 <br>● 사진 규칙: 반딧불이 관찰 시 플래시 사용 금지, 반딧불이 포획 금지 <br>● 보호 조치: 모기 방지 조치를 취해 주세요 <br>● 소요 시간: 약 75분 (각 경험 25분) <br>● 환불 정책: 폭우 시 전액 환불 <br>● 경험 순서: 투어는 일반적으로 한 번의 종합 보트 여행으로 세 가지 경험 모두를 포함합니다" },

    },

    // ============ 新增 SubQR 服务 (2025-01-27) ============
    "Hourly Charter (8 Hour) within Selangor": {
        "id": { 
            "description": "Layanan sewa mobil pribadi 8 jam di dalam wilayah Selangor dengan sopir profesional", 
            "remark": "● Penjemputan: Terbatas pada lokasi tertentu di Selangor <br>● Pemesanan: Sebelum pukul 8 malam di malam sebelum keberangkatan <br>● Durasi: Maksimal 8 jam <br>● Cakupan wilayah: Dalam batas Selangor <br>● Biaya termasuk: Parkir, Tol <br>● Makanan: Atur sendiri <br>● Biaya tambahan: Overtime & transfer bandara" 
        },
        "ms": { 
            "description": "Perkhidmatan sewa kereta peribadi 8 jam dalam wilayah Selangor dengan pemandu profesional", 
            "remark": "● Pengambilan: Terhad kepada lokasi yang ditetapkan di Selangor <br>● Tempahan: Sebelum 8 malam pada malam sebelum berlepas <br>● Tempoh: Maksimum 8 jam <br>● Kawasan liputan: Dalam sempadan Selangor <br>● Bayaran termasuk: Bayaran parkir, Bayaran tol <br>● Makanan: Atur sendiri <br>● Bayaran tambahan: Lebih masa & pemindahan lapangan terbang" 
        },
        "en": { 
            "description": "8-hour private charter service within Selangor state with professional driver", 
            "remark": "● Pick-up: Limited to designated Selangor locations <br>● Booking: Before 8 PM the night before departure <br>● Duration: Maximum 8 hours <br>● Coverage area: Within Selangor boundaries <br>● Included fees: Parking, Tolls <br>● Meals: Self-arranged <br>● Additional charges: Overtime & airport transfers" 
        },
        "vi": { 
            "description": "Dịch vụ thuê xe riêng 8 giờ trong bang Selangor với tài xế chuyên nghiệp", 
            "remark": "● Đón khách: Chỉ giới hạn tại các địa điểm chỉ định ở Selangor <br>● Đặt dịch vụ: Trước 8 giờ tối của đêm trước khi khởi hành <br>● Thời gian: Tối đa 8 giờ <br>● Khu vực phục vụ: Trong phạm vi Selangor <br>● Phí bao gồm: Phí đỗ xe, Phí đường <br>● Ăn uống: Tự túc <br>● Phí phụ thu: Vượt giờ & đưa đón sân bay" 
        },
        "ru": { 
            "description": "8-часовой сервис частного автомобиля в штате Селангор с профессиональным водителем", 
            "remark": "● Посадка: Ограничено указанными местами в Селангоре <br>● Бронирование: До 8 вечера накануне отправления <br>● Продолжительность: Максимум 8 часов <br>● Зона покрытия: В пределах Селангора <br>● Стоимость включает: Парковка, дорожные сборы <br>● Питание: Самостоятельно <br>● Дополнительная плата: Сверхурочные и трансферы в аэропорт" 
        },
        "th": { 
            "description": "บริการเช่ารถส่วนตัว 8 ชั่วโมงในรัฐเซลังงอร์พร้อมคนขับมืออาชีพ", 
            "remark": "● การรับส่ง: จำกัดเฉพาะสถานที่ที่กำหนดในเซลังงอร์ <br>● การจอง: ก่อน 8 โมงเย็นของคืนก่อนออกเดินทาง <br>● ระยะเวลา: สูงสุด 8 ชั่วโมง <br>● พื้นที่ให้บริการ: ภายในขอบเขตเซลังงอร์ <br>● ค่าใช้จ่ายรวม: ค่าจอดรถ, ค่าผ่านทาง <br>● อาหาร: จัดการเอง <br>● ค่าใช้จ่ายเพิ่มเติม: ค่าล่วงเวลา & รับส่งสนามบิน" 
        },
        "ja": { 
            "description": "セランゴール州内8時間プライベートチャーターサービス、プロドライバー付き", 
            "remark": "● 送迎: セランゴール州指定場所限定 <br>● 予約: 出発前夜の8時まで <br>● 時間: 最大8時間 <br>● サービス範囲: セランゴール州内 <br>● 料金に含まれるもの: 駐車場代、高速代 <br>● 食事: 各自手配 <br>● 追加料金: 時間超過・空港送迎" 
        },
        "zh-CN": { 
            "description": "雪兰莪州内8小时私人包车服务，配专业司机", 
            "remark": "● 接送: 仅限雪兰莪州指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 最多8小时 <br>● 服务范围: 雪兰莪州境内 <br>● 费用包含: 停车费、过路费 <br>● 餐食: 自理 <br>● 额外费用: 超时费 & 机场接送" 
        },
        "zh-TW": { 
            "description": "雪蘭莪州內8小時私人包車服務，配專業司機", 
            "remark": "● 接送: 僅限雪蘭莪州指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 最多8小時 <br>● 服務範圍: 雪蘭莪州境內 <br>● 費用包含: 停車費、過路費 <br>● 餐食: 自理 <br>● 額外費用: 超時費 & 機場接送" 
        },
        "ko": { 
            "description": "셀랑고르 주 내 8시간 전용 차량 서비스, 전문 기사 배정", 
            "remark": "● 픽업: 셀랑고르 주 지정 장소로 제한 <br>● 예약: 출발 전날 저녁 8시 이전 <br>● 시간: 최대 8시간 <br>● 서비스 범위: 셀랑고르 주 경계 내 <br>● 비용 포함: 주차비, 통행료 <br>● 식사: 개별 준비 <br>● 추가 요금: 초과 시간 & 공항 픽업" 
        }
    },

    "Hourly Charter (4 Hour) within Selangor": {
        "id": { 
            "description": "Layanan sewa mobil pribadi 4 jam di dalam wilayah Selangor dengan sopir profesional", 
            "remark": "● Penjemputan: Terbatas pada lokasi tertentu di Selangor <br>● Pemesanan: Sebelum pukul 8 malam di malam sebelum keberangkatan <br>● Durasi: Maksimal 4 jam <br>● Cakupan wilayah: Dalam batas Selangor <br>● Biaya termasuk: Parkir, Tol <br>● Makanan: Atur sendiri <br>● Biaya tambahan: Overtime & transfer bandara" 
        },
        "ms": { 
            "description": "Perkhidmatan sewa kereta peribadi 4 jam dalam wilayah Selangor dengan pemandu profesional", 
            "remark": "● Pengambilan: Terhad kepada lokasi yang ditetapkan di Selangor <br>● Tempahan: Sebelum 8 malam pada malam sebelum berlepas <br>● Tempoh: Maksimum 4 jam <br>● Kawasan liputan: Dalam sempadan Selangor <br>● Bayaran termasuk: Bayaran parkir, Bayaran tol <br>● Makanan: Atur sendiri <br>● Bayaran tambahan: Lebih masa & pemindahan lapangan terbang" 
        },
        "en": { 
            "description": "4-hour private charter service within Selangor state with professional driver", 
            "remark": "● Pick-up: Limited to designated Selangor locations <br>● Booking: Before 8 PM the night before departure <br>● Duration: Maximum 4 hours <br>● Coverage area: Within Selangor boundaries <br>● Included fees: Parking, Tolls <br>● Meals: Self-arranged <br>● Additional charges: Overtime & airport transfers" 
        },
        "vi": { 
            "description": "Dịch vụ thuê xe riêng 4 giờ trong bang Selangor với tài xế chuyên nghiệp", 
            "remark": "● Đón khách: Chỉ giới hạn tại các địa điểm chỉ định ở Selangor <br>● Đặt dịch vụ: Trước 8 giờ tối của đêm trước khi khởi hành <br>● Thời gian: Tối đa 4 giờ <br>● Khu vực phục vụ: Trong phạm vi Selangor <br>● Phí bao gồm: Phí đỗ xe, Phí đường <br>● Ăn uống: Tự túc <br>● Phí phụ thu: Vượt giờ & đưa đón sân bay" 
        },
        "ru": { 
            "description": "4-часовой сервис частного автомобиля в штате Селангор с профессиональным водителем", 
            "remark": "● Посадка: Ограничено указанными местами в Селангоре <br>● Бронирование: До 8 вечера накануне отправления <br>● Продолжительность: Максимум 4 часа <br>● Зона покрытия: В пределах Селангора <br>● Стоимость включает: Парковка, дорожные сборы <br>● Питание: Самостоятельно <br>● Дополнительная плата: Сверхурочные и трансферы в аэропорт" 
        },
        "th": { 
            "description": "บริการเช่ารถส่วนตัว 4 ชั่วโมงในรัฐเซลังงอร์พร้อมคนขับมืออาชีพ", 
            "remark": "● การรับส่ง: จำกัดเฉพาะสถานที่ที่กำหนดในเซลังงอร์ <br>● การจอง: ก่อน 8 โมงเย็นของคืนก่อนออกเดินทาง <br>● ระยะเวลา: สูงสุด 4 ชั่วโมง <br>● พื้นที่ให้บริการ: ภายในขอบเขตเซลังงอร์ <br>● ค่าใช้จ่ายรวม: ค่าจอดรถ, ค่าผ่านทาง <br>● อาหาร: จัดการเอง <br>● ค่าใช้จ่ายเพิ่มเติม: ค่าล่วงเวลา & รับส่งสนามบิน" 
        },
        "ja": { 
            "description": "セランゴール州内4時間プライベートチャーターサービス、プロドライバー付き", 
            "remark": "● 送迎: セランゴール州指定場所限定 <br>● 予約: 出発前夜の8時まで <br>● 時間: 最大4時間 <br>● サービス範囲: セランゴール州内 <br>● 料金に含まれるもの: 駐車場代、高速代 <br>● 食事: 各自手配 <br>● 追加料金: 時間超過・空港送迎" 
        },
        "zh-CN": { 
            "description": "雪兰莪州内4小时私人包车服务，配专业司机", 
            "remark": "● 接送: 仅限雪兰莪州指定地点 <br>● 预订: 出发前一晚8点前 <br>● 时长: 最多4小时 <br>● 服务范围: 雪兰莪州境内 <br>● 费用包含: 停车费、过路费 <br>● 餐食: 自理 <br>● 额外费用: 超时费 & 机场接送" 
        },
        "zh-TW": { 
            "description": "雪蘭莪州內4小時私人包車服務，配專業司機", 
            "remark": "● 接送: 僅限雪蘭莪州指定地點 <br>● 預訂: 出發前一晚8點前 <br>● 時長: 最多4小時 <br>● 服務範圍: 雪蘭莪州境內 <br>● 費用包含: 停車費、過路費 <br>● 餐食: 自理 <br>● 額外費用: 超時費 & 機場接送" 
        },
        "ko": { 
            "description": "셀랑고르 주 내 4시간 전용 차량 서비스, 전문 기사 배정", 
            "remark": "● 픽업: 셀랑고르 주 지정 장소로 제한 <br>● 예약: 출발 전날 저녁 8시 이전 <br>● 시간: 최대 4시간 <br>● 서비스 범위: 셀랑고르 주 경계 내 <br>● 비용 포함: 주차비, 통행료 <br>● 식사: 개별 준비 <br>● 추가 요금: 초과 시간 & 공항 픽업" 
        }
    },

    "Eco Venture Village Semenyih => Kuala Lumpur Area": {
        "id": { 
            "description": "Layanan transfer dari Eco Venture Village Semenyih ke area Kuala Lumpur", 
            "remark": "● Penjemputan: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Tujuan: Area Kuala Lumpur <br>● Pemesanan: Minimum 6 jam sebelum keberangkatan <br>● Surcharge malam: 15% untuk jam 00:00-07:00 <br>● Kapasitas: Pastikan jumlah penumpang dan bagasi sesuai batas kendaraan" 
        },
        "ms": { 
            "description": "Perkhidmatan pemindahan dari Eco Venture Village Semenyih ke kawasan Kuala Lumpur", 
            "remark": "● Pengambilan: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Destinasi: Kawasan Kuala Lumpur <br>● Tempahan: Minimum 6 jam sebelum berlepas <br>● Caj malam: 15% untuk jam 00:00-07:00 <br>● Had kapasiti: Pastikan jumlah penumpang dan bagasi tidak melebihi had kenderaan" 
        },
        "en": { 
            "description": "Transfer service from Eco Venture Village Semenyih to Kuala Lumpur Area", 
            "remark": "● Pick-up: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Destination: Kuala Lumpur Area <br>● Booking: Minimum 6 hours before departure <br>● Night surcharge: 15% for 00:00-07:00 hours <br>● Capacity limit: Ensure passenger count and luggage do not exceed vehicle limits" 
        },
        "vi": { 
            "description": "Dịch vụ đưa đón từ Eco Venture Village Semenyih đến khu vực Kuala Lumpur", 
            "remark": "● Điểm đón: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Điểm đến: Khu vực Kuala Lumpur <br>● Đặt xe: Tối thiểu 6 tiếng trước khi khởi hành <br>● Phụ phí đêm: 15% cho khung giờ 00:00-07:00 <br>● Giới hạn sức chứa: Đảm bảo số lượng hành khách và hành lý không vượt quá giới hạn xe" 
        },
        "ru": { 
            "description": "Трансферная служба из Eco Venture Village Semenyih в район Куала-Лумпур", 
            "remark": "● Место посадки: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Пункт назначения: Район Куала-Лумпур <br>● Бронирование: Минимум за 6 часов до отправления <br>● Доплата за ночь: 15% за время с 00:00 до 07:00 <br>● Ограничение вместимости: Убедитесь, что количество пассажиров и багажа не превышает лимиты автомобиля" 
        },
        "th": { 
            "description": "บริการรับส่งจาก Eco Venture Village Semenyih ไปยังพื้นที่กัวลาลัมเปอร์", 
            "remark": "● จุดรับ: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● จุดหมาย: พื้นที่กัวลาลัมเปอร์ <br>● การจอง: ขั้นต่ำ 6 ชั่วโมงก่อนออกเดินทาง <br>● ค่าธรรมเนียมกลางคืน: 15% สำหรับช่วงเวลา 00:00-07:00 น. <br>● ขีดจำกัดความจุ: ให้แน่ใจว่าจำนวนผู้โดยสารและสัมภาระไม่เกินขีดจำกัดของรถ" 
        },
        "ja": { 
            "description": "Eco Venture Village Semenyihからクアラルンプール地区への送迎サービス", 
            "remark": "● 乗車場所: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 目的地: クアラルンプール地区 <br>● 予約: 出発6時間前まで <br>● 夜間料金: 00:00-07:00は15%追加 <br>● 積載制限: 乗客数・荷物が車両制限を超えないよう確認" 
        },
        "zh-CN": { 
            "description": "从Eco Venture Village Semenyih至吉隆坡地区接送服务", 
            "remark": "● 接客地点: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 目的地: 吉隆坡地区 <br>● 预订: 出发前最少6小时 <br>● 夜间附加费: 00:00-07:00时段加收15% <br>● 载客限制: 确保乘客人数及行李不超过车辆限制" 
        },
        "zh-TW": { 
            "description": "從Eco Venture Village Semenyih至吉隆坡地區接送服務", 
            "remark": "● 接客地點: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 目的地: 吉隆坡地區 <br>● 預訂: 出發前最少6小時 <br>● 夜間附加費: 00:00-07:00時段加收15% <br>● 載客限制: 確保乘客人數及行李不超過車輛限制" 
        },
        "ko": { 
            "description": "Eco Venture Village Semenyih에서 쿠알라룸푸르 지역으로의 픽업 서비스", 
            "remark": "● 픽업 위치: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 목적지: 쿠알라룸푸르 지역 <br>● 예약: 출발 최소 6시간 전 <br>● 야간 할증료: 00:00-07:00 시간대 15% 추가 <br>● 적재 제한: 탑승 인원 및 수하물이 차량 제한을 초과하지 않도록 확인" 
        }
    },

    "Kuala Lumpur Area => Eco Venture Village Semenyih": {
        "id": { 
            "description": "Layanan transfer dari area Kuala Lumpur ke Eco Venture Village Semenyih", 
            "remark": "● Penjemputan: Area Kuala Lumpur <br>● Tujuan: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Pemesanan: Minimum 6 jam sebelum keberangkatan <br>● Surcharge malam: 15% untuk jam 00:00-07:00 <br>● Kapasitas: Pastikan jumlah penumpang dan bagasi sesuai batas kendaraan" 
        },
        "ms": { 
            "description": "Perkhidmatan pemindahan dari kawasan Kuala Lumpur ke Eco Venture Village Semenyih", 
            "remark": "● Pengambilan: Kawasan Kuala Lumpur <br>● Destinasi: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Tempahan: Minimum 6 jam sebelum berlepas <br>● Caj malam: 15% untuk jam 00:00-07:00 <br>● Had kapasiti: Pastikan jumlah penumpang dan bagasi tidak melebihi had kenderaan" 
        },
        "en": { 
            "description": "Transfer service from Kuala Lumpur Area to Eco Venture Village Semenyih", 
            "remark": "● Pick-up: Kuala Lumpur Area <br>● Destination: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Booking: Minimum 6 hours before departure <br>● Night surcharge: 15% for 00:00-07:00 hours <br>● Capacity limit: Ensure passenger count and luggage do not exceed vehicle limits" 
        },
        "vi": { 
            "description": "Dịch vụ đưa đón từ khu vực Kuala Lumpur đến Eco Venture Village Semenyih", 
            "remark": "● Điểm đón: Khu vực Kuala Lumpur <br>● Điểm đến: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Đặt xe: Tối thiểu 6 tiếng trước khi khởi hành <br>● Phụ phí đêm: 15% cho khung giờ 00:00-07:00 <br>● Giới hạn sức chứa: Đảm bảo số lượng hành khách và hành lý không vượt quá giới hạn xe" 
        },
        "ru": { 
            "description": "Трансферная служба из района Куала-Лумпур в Eco Venture Village Semenyih", 
            "remark": "● Место посадки: Район Куала-Лумпур <br>● Пункт назначения: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Бронирование: Минимум за 6 часов до отправления <br>● Доплата за ночь: 15% за время с 00:00 до 07:00 <br>● Ограничение вместимости: Убедитесь, что количество пассажиров и багажа не превышает лимиты автомобиля" 
        },
        "th": { 
            "description": "บริการรับส่งจากพื้นที่กัวลาลัมเปอร์ไปยัง Eco Venture Village Semenyih", 
            "remark": "● จุดรับ: พื้นที่กัวลาลัมเปอร์ <br>● จุดหมาย: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● การจอง: ขั้นต่ำ 6 ชั่วโมงก่อนออกเดินทาง <br>● ค่าธรรมเนียมกลางคืน: 15% สำหรับช่วงเวลา 00:00-07:00 น. <br>● ขีดจำกัดความจุ: ให้แน่ใจว่าจำนวนผู้โดยสารและสัมภาระไม่เกินขีดจำกัดของรถ" 
        },
        "ja": { 
            "description": "クアラルンプール地区からEco Venture Village Semenyihへの送迎サービス", 
            "remark": "● 乗車場所: クアラルンプール地区 <br>● 目的地: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 予約: 出発6時間前まで <br>● 夜間料金: 00:00-07:00は15%追加 <br>● 積載制限: 乗客数・荷物が車両制限を超えないよう確認" 
        },
        "zh-CN": { 
            "description": "从吉隆坡地区至Eco Venture Village Semenyih接送服务", 
            "remark": "● 接客地点: 吉隆坡地区 <br>● 目的地: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 预订: 出发前最少6小时 <br>● 夜间附加费: 00:00-07:00时段加收15% <br>● 载客限制: 确保乘客人数及行李不超过车辆限制" 
        },
        "zh-TW": { 
            "description": "從吉隆坡地區至Eco Venture Village Semenyih接送服務", 
            "remark": "● 接客地點: 吉隆坡地區 <br>● 目的地: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 預訂: 出發前最少6小時 <br>● 夜間附加費: 00:00-07:00時段加收15% <br>● 載客限制: 確保乘客人數及行李不超過車輛限制" 
        },
        "ko": { 
            "description": "쿠알라룸푸르 지역에서 Eco Venture Village Semenyih로의 픽업 서비스", 
            "remark": "● 픽업 위치: 쿠알라룸푸르 지역 <br>● 목적지: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 예약: 출발 최소 6시간 전 <br>● 야간 할증료: 00:00-07:00 시간대 15% 추가 <br>● 적재 제한: 탑승 인원 및 수하물이 차량 제한을 초과하지 않도록 확인" 
        }
    },

    "KLIA => Eco Venture Village Semenyih": {
        "id": { 
            "description": "Layanan transfer dari Bandara Kuala Lumpur ke Eco Venture Village Semenyih", 
            "remark": "● Penjemputan: KL International Airport (KUL), Sepang, Selangor <br>● Tujuan: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Waktu tunggu gratis: 90 menit berdasarkan jadwal penerbangan <br>● Surcharge malam: 15% untuk jam 00:00-07:00 <br>● Pemesanan: Minimum 6 jam sebelum keberangkatan <br>● Info penerbangan: Diperlukan" 
        },
        "ms": { 
            "description": "Perkhidmatan pemindahan dari Lapangan Terbang Kuala Lumpur ke Eco Venture Village Semenyih", 
            "remark": "● Pengambilan: KL International Airport (KUL), Sepang, Selangor <br>● Destinasi: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Masa menunggu percuma: 90 minit berdasarkan jadual penerbangan <br>● Caj malam: 15% untuk jam 00:00-07:00 <br>● Tempahan: Minimum 6 jam sebelum berlepas <br>● Maklumat penerbangan: Diperlukan" 
        },
        "en": { 
            "description": "Transfer service from Kuala Lumpur Airport to Eco Venture Village Semenyih", 
            "remark": "● Pick-up: KL International Airport (KUL), Sepang, Selangor <br>● Destination: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Free waiting time: 90 minutes based on flight schedule <br>● Night surcharge: 15% for 00:00-07:00 hours <br>● Booking: Minimum 6 hours before departure <br>● Flight information: Required" 
        },
        "vi": { 
            "description": "Dịch vụ đưa đón từ Sân bay Kuala Lumpur đến Eco Venture Village Semenyih", 
            "remark": "● Điểm đón: KL International Airport (KUL), Sepang, Selangor <br>● Điểm đến: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Thời gian chờ miễn phí: 90 phút dựa trên lịch trình chuyến bay <br>● Phụ phí đêm: 15% cho khung giờ 00:00-07:00 <br>● Đặt xe: Tối thiểu 6 tiếng trước khi khởi hành <br>● Thông tin chuyến bay: Bắt buộc" 
        },
        "ru": { 
            "description": "Трансферная служба из аэропорта Куала-Лумпур в Eco Venture Village Semenyih", 
            "remark": "● Место посадки: KL International Airport (KUL), Sepang, Selangor <br>● Пункт назначения: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Бесплатное ожидание: 90 минут в соответствии с расписанием рейсов <br>● Доплата за ночь: 15% за время с 00:00 до 07:00 <br>● Бронирование: Минимум за 6 часов до отправления <br>● Информация о рейсе: Обязательна" 
        },
        "th": { 
            "description": "บริการรับส่งจากสนามบินกัวลาลัมเปอร์ไปยัง Eco Venture Village Semenyih", 
            "remark": "● จุดรับ: KL International Airport (KUL), Sepang, Selangor <br>● จุดหมาย: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● เวลารอฟรี: 90 นาทีตามตารางเที่ยวบิน <br>● ค่าธรรมเนียมกลางคืน: 15% สำหรับช่วงเวลา 00:00-07:00 น. <br>● การจอง: ขั้นต่ำ 6 ชั่วโมงก่อนออกเดินทาง <br>● ข้อมูลเที่ยวบิน: จำเป็น" 
        },
        "ja": { 
            "description": "クアラルンプール空港からEco Venture Village Semenyihへの送迎サービス", 
            "remark": "● 乗車場所: KL International Airport (KUL), Sepang, Selangor <br>● 目的地: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 無料待機時間: フライトスケジュールに基づく90分 <br>● 夜間料金: 00:00-07:00は15%追加 <br>● 予約: 出発6時間前まで <br>● フライト情報: 必須" 
        },
        "zh-CN": { 
            "description": "吉隆坡机场至Eco Venture Village Semenyih接送服务", 
            "remark": "● 接客地点: KL International Airport (KUL), Sepang, Selangor <br>● 目的地: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 免费等待时间: 根据航班时间90分钟 <br>● 夜间附加费: 00:00-07:00时段加收15% <br>● 预订: 出发前最少6小时 <br>● 航班信息: 必填" 
        },
        "zh-TW": { 
            "description": "吉隆坡機場至Eco Venture Village Semenyih接送服務", 
            "remark": "● 接客地點: KL International Airport (KUL), Sepang, Selangor <br>● 目的地: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 免費等待時間: 根據航班時間90分鐘 <br>● 夜間附加費: 00:00-07:00時段加收15% <br>● 預訂: 出發前最少6小時 <br>● 航班資訊: 必填" 
        },
        "ko": { 
            "description": "쿠알라룸푸르 공항에서 Eco Venture Village Semenyih로의 픽업 서비스", 
            "remark": "● 픽업 위치: KL International Airport (KUL), Sepang, Selangor <br>● 목적지: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 무료 대기 시간: 항공편 일정에 따라 90분 <br>● 야간 할증료: 00:00-07:00 시간대 15% 추가 <br>● 예약: 출발 최소 6시간 전 <br>● 항공편 정보: 필수" 
        }
    },

    "Eco Venture Village => Semenyih KLIA": {
        "id": { 
            "description": "Layanan transfer dari Eco Venture Village Semenyih ke Bandara Kuala Lumpur", 
            "remark": "● Penjemputan: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Tujuan: KL International Airport (KUL), Sepang, Selangor <br>● Waktu tunggu gratis: 30 menit sesuai waktu pesanan <br>● Surcharge malam: 15% untuk jam 00:00-07:00 <br>● Pemesanan: Minimum 6 jam sebelum keberangkatan <br>● Info penerbangan: Diperlukan untuk waktu keberangkatan yang tepat" 
        },
        "ms": { 
            "description": "Perkhidmatan pemindahan dari Eco Venture Village Semenyih ke Lapangan Terbang Kuala Lumpur", 
            "remark": "● Pengambilan: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Destinasi: KL International Airport (KUL), Sepang, Selangor <br>● Masa menunggu percuma: 30 minit mengikut masa tempahan <br>● Caj malam: 15% untuk jam 00:00-07:00 <br>● Tempahan: Minimum 6 jam sebelum berlepas <br>● Maklumat penerbangan: Diperlukan untuk masa berlepas yang tepat" 
        },
        "en": { 
            "description": "Transfer service from Eco Venture Village Semenyih to Kuala Lumpur Airport", 
            "remark": "● Pick-up: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Destination: KL International Airport (KUL), Sepang, Selangor <br>● Free waiting time: 30 minutes based on order time <br>● Night surcharge: 15% for 00:00-07:00 hours <br>● Booking: Minimum 6 hours before departure <br>● Flight information: Required for accurate departure timing" 
        },
        "vi": { 
            "description": "Dịch vụ đưa đón từ Eco Venture Village Semenyih đến Sân bay Kuala Lumpur", 
            "remark": "● Điểm đón: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Điểm đến: KL International Airport (KUL), Sepang, Selangor <br>● Thời gian chờ miễn phí: 30 phút tính từ giờ đặt xe <br>● Phụ phí đêm: 15% cho khung giờ 00:00-07:00 <br>● Đặt xe: Tối thiểu 6 tiếng trước khi khởi hành <br>● Thông tin chuyến bay: Cần thiết để đưa đón đúng giờ" 
        },
        "ru": { 
            "description": "Трансферная служба из Eco Venture Village Semenyih в аэропорт Куала-Лумпур", 
            "remark": "● Место посадки: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● Пункт назначения: KL International Airport (KUL), Sepang, Selangor <br>● Бесплатное ожидание: 30 минут согласно времени заказа <br>● Доплата за ночь: 15% за время с 00:00 до 07:00 <br>● Бронирование: Минимум за 6 часов до отправления <br>● Информация о рейсе: Требуется для точного времени отправления" 
        },
        "th": { 
            "description": "บริการรับส่งจาก Eco Venture Village Semenyih ไปยังสนามบินกัวลาลัมเปอร์", 
            "remark": "● จุดรับ: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● จุดหมาย: KL International Airport (KUL), Sepang, Selangor <br>● เวลารอฟรี: 30 นาทีตามเวลาที่สั่งจอง <br>● ค่าธรรมเนียมกลางคืน: 15% สำหรับช่วงเวลา 00:00-07:00 น. <br>● การจอง: ขั้นต่ำ 6 ชั่วโมงก่อนออกเดินทาง <br>● ข้อมูลเที่ยวบิน: จำเป็นสำหรับเวลาออกเดินทางที่ถูกต้อง" 
        },
        "ja": { 
            "description": "Eco Venture Village Semenyihからクアラルンプール空港への送迎サービス", 
            "remark": "● 乗車場所: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 目的地: KL International Airport (KUL), Sepang, Selangor <br>● 無料待機時間: 30分 ご注文時間による <br>● 夜間料金: 00:00-07:00は15%追加 <br>● 予約: 出発6時間前まで <br>● フライト情報: 正確な出発時間のため必要" 
        },
        "zh-CN": { 
            "description": "Eco Venture Village Semenyih至吉隆坡机场接送服务", 
            "remark": "● 接客地点: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 目的地: KL International Airport (KUL), Sepang, Selangor <br>● 免费等待时间: 30分钟 根据订单时间 <br>● 夜间附加费: 00:00-07:00时段加收15% <br>● 预订: 出发前最少6小时 <br>● 航班信息: 准确出发时间必填" 
        },
        "zh-TW": { 
            "description": "Eco Venture Village Semenyih至吉隆坡機場接送服務", 
            "remark": "● 接客地點: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 目的地: KL International Airport (KUL), Sepang, Selangor <br>● 免費等待時間: 30分鐘 依訂單時間 <br>● 夜間附加費: 00:00-07:00時段加收15% <br>● 預訂: 出發前最少6小時 <br>● 航班資訊: 準確出發時間必填" 
        },
        "ko": { 
            "description": "Eco Venture Village Semenyih에서 쿠알라룸푸르 공항으로의 픽업 서비스", 
            "remark": "● 픽업 위치: Eco Venture Village, Jalan Sungai Lalang, Semenyih, Selangor <br>● 목적지: KL International Airport (KUL), Sepang, Selangor <br>● 무료 대기 시간: 30분 주문 시간 기준 <br>● 야간 할증료: 00:00-07:00 시간대 15% 추가 <br>● 예약: 출발 최소 6시간 전 <br>● 항공편 정보: 정확한 출발 시간을 위해 필수" 
        }
    }

};

/** ================== 模糊匹配辅助初始化 ================== */
function normalizeTitle(title) {
    return String(title)
        .toLowerCase()
        .replace(/\s+/g, '')       // 忽略空格
        .replace(/hours/g, 'hour')  // 统一单复数
        .replace(/fireflies/g, 'firefly')  // 专项处理 firefly 的各种写法/复数
        .replace(/fireflie/g, 'firefly')
        .replace(/ies$/, 'y')
        .replace(/s$/, '')
        .replace(/[^a-z0-9]/g, ''); // 移除非字母数字字符
}
window.normalizeTitle = normalizeTitle;

if (!window.translationKeyMap) {
    window.translationKeyMap = {};
    Object.keys(translationDataSource).forEach(key => {
        window.translationKeyMap[normalizeTitle(key)] = key;
    });
    console.log(`🔍 已构建 translationKeyMap (共 ${Object.keys(window.translationKeyMap).length} 条)`);
}

// ====================================================================================
// 脚本启动点 (Script Entry Point) - v3.0 智能模式示例
// ====================================================================================

// 🧠 智能模式（推荐）：自动判断添加或编辑
await autoQR.runBatchForCurrentModal(['id', 'ms', 'en', 'vi', 'ru', 'th', 'ja', 'zh-CN', 'zh-TW', 'ko'], { smartMode: true });

// 🚚 传统模式（向后兼容）：仅添加新翻译
// await autoQR.runBatchForCurrentModal(['id', 'ms', 'en', 'vi', 'ru', 'th', 'ja', 'zh-CN', 'zh-TW', 'ko'], { legacyMode: true });

// 📝 直接调用智能处理（需要手动指定服务标题）
// await autoQR.smartBatchProcess('Eagle Feeding Ticket', ['en', 'zh-CN', 'ms']);
